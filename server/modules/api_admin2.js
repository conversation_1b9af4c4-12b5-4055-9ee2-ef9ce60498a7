import express from 'express';
import EventEmitter from 'events';
import rateLimit from 'express-rate-limit';
import multer from 'multer';

// ** ------------------------------------------------------------------------------------------------
//** PDF API (pdfmake) */

import { SingleCertificate } from './ops2/pdf/certificate/single_certificate.js';
import { OverallCertificate } from './ops2/pdf/certificate/overall_certificate.js';
import { MergedCertificate } from './ops2/pdf/certificate/merged_certificate.js';

// ** PDF API (pdf-lib) */
import { GenerateAssessmentVoucherPDF } from './ops2/pdf/assessment_voucher/index.js';

// ** EXCEL API */
import { Excel_GenerateReport } from './ops2/spreadsheet/generate_report.js';

// ** ------------------------------------------------------------------------------------------------
//** OPS */

import {
	Login,
	Logout,
	ValidateToken,
	UpdateSession
} from './ops2/login_ops.js';

import {
	UserOps,
	GetUsers,
	ResetPassword,
	UpdatePasswordByCode,
	CheckResetCode
} from './ops2/user_ops.js';

import {
	DashboardCards,
	GetOnlineUsers,
	GetSystemLogs,
	GetDashboardTests
} from './ops2/dashboard_ops.js';

import {
	GetCandidate,
	GetCandidateInfo,
	EditCandidate,
	GetTestTakers,
	GetSpeedTestResults,
	GetTestsByOverallGroup,
	UpdateCandidatePhoto
} from './ops2/candidate_ops.js';

import {
	GetCompany,
	UpdateCompany,
	AddCompany,
	DeleteCompany,
	GetCompanies,
	GetTokenReportReceivers,
	SendReport2Company,
	GetCompanyWithAvailTokens,
	GetCompanyContactGroups,
	CompanyContactOps,
	GetCompanyDocuments,
	CreateCompanyDocument,
	DeleteCompanyDocument,
	DownloadCompanyDocument
} from './ops2/company_ops.js';

import { GetContactAssignments } from './ops2/company_assignments_ops.js';

import { GenerateTokens } from './ops2/generate_tokens.js';

import {
	GetTestVersions,
	GetCandidateTestAnswers,
	GetTestContents,
	GetAudioBase64
} from './ops2/test_ops.js';

import { GenerateReport } from './ops2/report.js';

import {
	GetTestTokens,
} from './ops2/test_tokens_ops.js';

import {
	UpdateTestTokenSettings,
	GetTestTokenDetails
} from './ops2/test_token_ops.js';

import {
	DashboardCharts
} from './ops2/chart_ops.js';


import {
	GetAssessors,
	GetAssessorPayments
} from './ops2/assessor_ops.js';

import {
	GetAssessments
} from './ops2/assessments_ops.js';

import {
	sendTestInvitation,
	sendInvitationEmail
} from './ops2/invitation_ops.js';

import {
	GetMobeseCount,
	GetMobeseRecords,
	GetSecurityCheckList,
	CompleteSecurityCheck,
	GetVideoInfo
} from './ops2/test_security_ops.js';


import { GetWebcamVideos } from './cdn/bunnycdn.js';
import { NavSearch } from './ops2/nav_search.js';
import { GetAIDetectionTokens, GetAIDetectionDashboard } from './ops2/aidetection_ops.js';
import { GetActiveCandidatesCount } from './socket/index.js';

// ** APIs
import { brevo_api } from './email.js';
import { TawkAPI } from './ops2/tawk/index.js';
import { FirebaseAPI } from './firebase/index.js';

// ** ------------------------------------------------------------------------------------------------

const router = express.Router();
const Bus = new EventEmitter();
Bus.setMaxListeners(100);

const isDev = process.env.NODE_ENV === 'development';
const checkStartDate = '2022-01-01 00:00:00';

// ------------------------------
// Utils:

const returner = async (res, req, data) => {
	res.status(200).json({
		...data,
		...(isDev ? {
			req_body: req.body
		} : {})
	});
};

const REQUEST_LIMIT = 85;
const limiter = rateLimit({
	windowMs: 60 * 1000,
	max: REQUEST_LIMIT,
	keyGenerator: (req) => req.ip,
	message: {
		status: 'error',
		message: 'Too many requests, please try again later.'
	}
});

// Specific rate limiter for GetTestsByOverallGroup to prevent infinite loops
const testsByOverallGroupLimiter = rateLimit({
	windowMs: 10 * 1000, // 10 seconds
	max: 3, // Max 3 requests per 10 seconds
	keyGenerator: (req) => `${req.ip}-${req.body?.email}-${req.body?.overall_group}`,
	message: {
		status: 'error',
		message: 'Too many requests for this endpoint. Please wait before trying again.'
	},
	skip: (req) => isDev // Skip rate limiting in development
});

// Specific rate limiter for GetTokenReportReceivers to prevent infinite loops
const tokenReportReceiversLimiter = rateLimit({
	windowMs: 10 * 1000, // 10 seconds
	max: 3, // Max 3 requests per 10 seconds
	keyGenerator: (req) => `${req.ip}-${req.body?.token}`,
	message: {
		status: 'error',
		message: 'Too many requests for GetTokenReportReceivers. Please wait before trying again.'
	},
	skip: (req) => isDev // Skip rate limiting in development
});

if (!isDev) {
	router.use(limiter);
}
const upload = multer();
const uploadFile = multer({ storage: multer.memoryStorage() });

// File upload endpoint for candidate avatars
router.post('/upload-avatar', uploadFile.single('file'), async (req, res) => {
	try {
		const file = req.file;
		const { token } = req.body;

		if (!file) {
			return res.status(400).json({ status: 'error', message: 'No file uploaded' });
		}
		if (!token) {
			return res.status(400).json({ status: 'error', message: 'Token is required' });
		}

		// Import dependencies
		const { zoneKeys } = await import('./functions.js');
		const { GetCandidate } = await import('./ops2/candidate_ops.js');
		const fetch = (await import('node-fetch')).default;

		// Check if candidate has existing photo and delete it from CDN
		try {
			const candidateData = await GetCandidate({ token });
			if (candidateData.status === 'success' && candidateData.data?.candidate?.photo) {
				const oldFileName = candidateData.data.candidate.photo;
				const oldFileUrl = `https://storage.bunnycdn.com/cepatest/${token}/${oldFileName}`;

				console.log(`[upload-avatar] Deleting old avatar: ${oldFileName}`);

				const deleteResponse = await fetch(oldFileUrl, {
					method: 'DELETE',
					headers: {
						'AccessKey': zoneKeys['cepatest'],
					},
				});

				if (deleteResponse.ok) {
					console.log(`[upload-avatar] Successfully deleted old avatar: ${oldFileName}`);
				} else {
					console.warn(`[upload-avatar] Failed to delete old avatar: ${deleteResponse.statusText}`);
				}
			}
		} catch (deleteError) {
			console.warn('[upload-avatar] Error checking/deleting old avatar:', deleteError.message);
			// Continue with upload even if deletion fails
		}

		// Generate filename
		const fileName = `${Date.now()}_avatar.jpg`;
		const zone = 'cepatest';
		const filePath = `/${token}/${fileName}`;

		// Upload new file to BunnyCDN
		const url = `https://storage.bunnycdn.com/${zone}${filePath}`;
		const uploadOptions = {
			method: 'PUT',
			body: file.buffer,
			headers: {
				'AccessKey': zoneKeys[zone],
				'Content-Type': file.mimetype || 'image/jpeg',
			},
		};

		const response = await fetch(url, uploadOptions);

		if (!response.ok) {
			console.error('[upload-avatar] CDN Error:', response.statusText);
			return res.status(500).json({
				status: 'error',
				message: `CDN upload failed: ${response.statusText}`
			});
		}

		// Update database
		const updateResult = await UpdateCandidatePhoto({ token, photo_filename: fileName });

		if (updateResult.status === 'success') {
			return res.json({
				status: 'success',
				message: 'Avatar uploaded and database updated successfully',
				data: {
					fileName: fileName,
					url: `https://cepatest.b-cdn.net/${token}/${encodeURIComponent(fileName)}`,
					dbUpdate: updateResult
				}
			});
		} else {
			return res.status(500).json({
				status: 'error',
				message: 'File uploaded but database update failed: ' + updateResult.message
			});
		}

	} catch (error) {
		console.error('[upload-avatar] Error:', error);
		return res.status(500).json({
			status: 'error',
			message: 'Upload failed: ' + error.message
		});
	}
});

router.use('/:op', upload.none(), async (req, res) => {

	const op = req.params.op;
	const values = req.body;
	// Best-practice way to extract client IP
	const ip =
		req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
		req.socket?.remoteAddress;

	switch (op) {
		// * ------------------------------------------------------------------------
		// * COMPANY CONTACT ASSIGNMENTS
		// * ------------------------------------------------------------------------
		case 'GetContactAssignments':
			returner(res, req, await GetContactAssignments(values));
			break;

		//* ------------------------------------------------------------------------
		//* OTHERS
		//* ------------------------------------------------------------------------

		case 'NavSearch':
			returner(res, req, await NavSearch(values));
			break;

		case 'BrevoAPI':
			returner(res, req, await brevo_api(values?.endpoint, values?.method, values?.params));
			break;

		case 'TawkAPI':
			returner(res, req, await TawkAPI(values));
			break;

		case 'FirebaseAPI':
			returner(res, req, await FirebaseAPI(values));
			break;

		case 'GetActiveCandidatesCount':
			returner(res, req, await GetActiveCandidatesCount());
			break;

		//* ------------------------------------------------------------------------
		//* LOGIN
		//* ------------------------------------------------------------------------

		case 'Login':
			returner(res, req, await Login({ ...values, ip }));
			break;

		case 'Logout':
			returner(res, req, await Logout(values));
			break;

		case 'validate-token':
			returner(res, req, await ValidateToken(values));
			break;

		case 'UpdateSession':
			returner(res, req, await UpdateSession(values));
			break;

		//* ------------------------------------------------------------------------
		//* USER OPS
		//* ------------------------------------------------------------------------

		case 'UserOps':
			returner(res, req, await UserOps(values));
			break;

		case 'GetUsers':
			returner(res, req, await GetUsers(values));
			break;

		case 'ResetPassword':
			returner(res, req, await ResetPassword(values));
			break;

		case 'UpdatePasswordByCode':
			returner(res, req, await UpdatePasswordByCode(values));
			break;

		case 'CheckResetCode':
			returner(res, req, await CheckResetCode(values));
			break;

		//* ------------------------------------------------------------------------
		//* DASHBOARD
		//* ------------------------------------------------------------------------

		case 'DashboardCards':
			returner(res, req, await DashboardCards(values));
			break;

		case 'GetOnlineUsers':
			returner(res, req, await GetOnlineUsers(values));
			break;

		case 'GetSystemLogs':
			returner(res, req, await GetSystemLogs(values));
			break;

		case 'GetDashboardTests':
			returner(res, req, await GetDashboardTests(values));
			break;

		//* ------------------------------------------------------------------------
		//* CHARTS
		//* ------------------------------------------------------------------------

		case 'DashboardCharts':
			returner(res, req, await DashboardCharts(values));
			break;

		//* ------------------------------------------------------------------------
		//* TEST TOKENS
		//* ------------------------------------------------------------------------

		case 'TestTokens':
			returner(res, req, await GetTestTokens(values));
			break;

		case 'UpdateTestTokenSettings':
			returner(res, req, await UpdateTestTokenSettings(values));
			break;

		case 'GetTestTokenDetails':
			returner(res, req, await GetTestTokenDetails(values));
			break;

		//* ------------------------------------------------------------------------
		//* CANDIDATE ENDPOINTS
		//* ------------------------------------------------------------------------

		case 'GetTestTakers':
			returner(res, req, await GetTestTakers(values));
			break;

		case 'GetCandidate':
			returner(res, req, await GetCandidate(values));
			break;

		case 'GetCandidateInfo':
			returner(res, req, await GetCandidateInfo(values));
			break;

		case 'EditCandidate':
			returner(res, req, await EditCandidate(values));
			break;

		case 'UpdateCandidatePhoto':
			returner(res, req, await UpdateCandidatePhoto(values));
			break;

		case 'GetSpeedTestResults':
			returner(res, req, await GetSpeedTestResults(values));
			break;

		case 'GetTestsByOverallGroup':
			// Apply specific rate limiting for this endpoint
			if (!isDev) {
				testsByOverallGroupLimiter(req, res, async () => {
					returner(res, req, await GetTestsByOverallGroup(values));
				});
			} else {
				returner(res, req, await GetTestsByOverallGroup(values));
			}
			break;

		//* ------------------------------------------------------------------------
		//* COMPANY ENDPOINTS
		//* ------------------------------------------------------------------------

		case 'GetCompany':
			returner(res, req, await GetCompany(values));
			break;

		case 'UpdateCompany':
			returner(res, req, await UpdateCompany(values));
			break;

		case 'AddCompany':
			returner(res, req, await AddCompany(values));
			break;

		case 'DeleteCompany':
			returner(res, req, await DeleteCompany(values));
			break;

		case 'GetCompanies':
			returner(res, req, await GetCompanies(values));
			break;

		case 'GetTokenReportReceivers':
			// Apply specific rate limiting for this endpoint
			if (!isDev) {
				tokenReportReceiversLimiter(req, res, async () => {
					returner(res, req, await GetTokenReportReceivers(values));
				});
			} else {
				returner(res, req, await GetTokenReportReceivers(values));
			}
			break;

		case 'SendReport2Company': // to company HR
			returner(res, req, await SendReport2Company(values));
			break;

		case 'GetCompanyWithAvailTokens':
			returner(res, req, await GetCompanyWithAvailTokens(values));
			break;

		case 'GenerateTokens':
			returner(res, req, await GenerateTokens(values));
			break;

		case 'GetCompanyContactGroups':
			returner(res, req, await GetCompanyContactGroups(values));
			break;

		case 'CompanyContactOps':
			returner(res, req, await CompanyContactOps(values));
			break;

		//* ------------------------------------------------------------------------
		//* COMPANY DOCUMENTS ENDPOINTS
		//* ------------------------------------------------------------------------

		case 'GetCompanyDocuments':
			returner(res, req, await GetCompanyDocuments(values));
			break;

		case 'CreateCompanyDocument':
			returner(res, req, await CreateCompanyDocument(values));
			break;

		case 'DeleteCompanyDocument':
			returner(res, req, await DeleteCompanyDocument(values));
			break;

		case 'DownloadCompanyDocument':
			returner(res, req, await DownloadCompanyDocument(values));
			break;

		//* ------------------------------------------------------------------------
		// ** Security Check Endpoints
		//* ------------------------------------------------------------------------

		case 'GetMobeseCount':
			returner(res, req, await GetMobeseCount(values));
			break;

		case 'GetSecurityCheckList':
			returner(res, req, await GetSecurityCheckList(values));
			break;

		case 'CompleteSecurityCheck':
			returner(res, req, await CompleteSecurityCheck(values));
			break;

		case 'GetVideoInfo':
			returner(res, req, await GetVideoInfo(values));
			break;

		case 'GetMobeseRecords': //! Deprecated
			returner(res, req, await GetMobeseRecords(values));
			break;

		//* ------------------------------------------------------------------------
		//* BunnyCDN Endpoints
		//* ------------------------------------------------------------------------

		case 'GetWebcamVideos':
			returner(res, req, await GetWebcamVideos(values));
			break;

		//* ------------------------------------------------------------------------
		//* Test Endpoints
		//* ------------------------------------------------------------------------

		case 'GetTestContents':
			returner(res, req, await GetTestContents(values));
			break;

		case 'GetAudioBase64':
			returner(res, req, await GetAudioBase64(values));
			break;

		case 'GetTestVersions':
			returner(res, req, await GetTestVersions(values));
			break;

		case 'GetCandidateTestAnswers':
			returner(res, req, await GetCandidateTestAnswers(values));
			break;

		//* ------------------------------------------------------------------------
		//* ASSESSOR ENDPOINTS
		//* ------------------------------------------------------------------------

		case 'GetAssessors':
			returner(res, req, await GetAssessors(values));
			break;

		case 'GetAssessorPayments':
			returner(res, req, await GetAssessorPayments(values));
			break;

		//* ------------------------------------------------------------------------
		//* ASSESSMENT ENDPOINTS
		//* ------------------------------------------------------------------------

		case 'GetAssessments':
			returner(res, req, await GetAssessments(values));
			break;

		//* ------------------------------------------------------------------------
		//* INVITATION ENDPOINTS
		//* ------------------------------------------------------------------------

		case 'SendTestInvitation':
			returner(res, req, await sendTestInvitation(values));
			break;

		case 'ReSendTestInvitation':
			returner(res, req, await sendInvitationEmail(values));
			break;

		//* ------------------------------------------------------------------------
		//* REPORT ENDPOINTS
		//* ------------------------------------------------------------------------

		case 'GenerateReport':
			returner(res, req, await GenerateReport(values));
			break;

		//* ------------------------------------------------------------------------
		//* AI DETECTION ENDPOINTS
		//* ------------------------------------------------------------------------

		case 'GetAIDetectionDashboard':
			returner(res, req, await GetAIDetectionDashboard(values));
			break;

		case 'GetAIDetectionTokens':
			returner(res, req, await GetAIDetectionTokens(values));
			break;

		//* ------------------------------------------------------------------------
		//* EXCEL API
		//* ------------------------------------------------------------------------

		case 'Excel_GenerateReport':
			returner(res, req, await Excel_GenerateReport(values));
			break;

		//* ------------------------------------------------------------------------
		//* PDF API
		//* ------------------------------------------------------------------------

		case 'GenerateAssessmentVoucherPDF':
			returner(res, req, await GenerateAssessmentVoucherPDF(values));
			break;

		case 'GenerateCertificate':

			const type = req?.body?.type;
			// console.log('Generating certificate:', req.body);

			if (type === 'single') {
				returner(res, req, await SingleCertificate(values));

			} else if (type === 'overall') {
				returner(res, req, await OverallCertificate(values));

			} else if (type === 'merged') {
				returner(res, req, await MergedCertificate(values));

			} else {
				res.status(401).json({
					status: 'error',
					message: 'Bad request',
					...(isDev ? { status_code: 401 } : {})
				});
			}

			break;
		// --------------------------------

		default:
			res.status(401).json({
				status: 'error',
				message: 'Bad request',
				...(isDev ? { status_code: 401 } : {})
			});
	}
});

export default router;