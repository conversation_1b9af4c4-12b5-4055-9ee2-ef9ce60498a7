import express from 'express';
const router = express.Router();

import jwt from 'jsonwebtoken';
import { post } from './mysql/mysql.js';
import { getFailStatus, getNextLevel, getNextLevelData, getNewQuestion, updateQuestionAndTime } from './test_functions.js';

import { jwtSecret } from './config.js';
import { send_email } from './email.js';
import { getCurrentDateTimeInTurkey } from './functions.js';

import soap from 'soap';
import moment from 'moment';

import fetch from 'node-fetch';

import { EventEmitter } from 'events';
const Bus = new EventEmitter();
Bus.setMaxListeners(100);

router.use(express.raw({
	type: 'application/octet-stream',
	limit: '5mb'
}));


const is_dev = process.env.NODE_ENV === 'development';
const now = getCurrentDateTimeInTurkey();

const storageZone = 'rv-cepatest';
const bunnycdnApiKey = '7aca4474-471c-4329-a853f7032004-87ee-4104';

const emailTemplateId = 246;

// ====================================================================================================

router.use('/:op', async (req, res) => {

	const op = req.params.op;

	const {
		dev,
		buffer,
		id,
		id1,
		id2,
		event,
		token,
		lang,
		email,
		company_id,
		year,
		test_type,
		test_version,
		area_code,
		date_of_birth,
		department,
		education,
		gender,
		job_position,
		lastname,
		mother_language,
		name,
		nationality,
		phone,
		tckn,
		test_take_reason,
		tckn_verification,
		question,
		seconds,
		blocked_time,
		stars,
		feedback,
		jwt_token,
		flag,
		level,
		column,
		value,
		next_level,
		question_rank,
		question_id,
		answer,
		final_question,
		file,
		start_date,
		db_event,
		date,
		logo,
		value1,
		value2,
		value3,
		value4,
		value5,
		value6,
		value7,
		value8,
		value9,
		value10,
		value11,
		value12,
		value13,
		value14,
		value15,
		value16,
		value17,
		value18,
		value19,
		value20,
		value21,
		value22,
		value23,
		value26,
		value27,
		value69,
		value70
	} = req.body;

	const rq_token = req.query.token;
	const rq_fileName = req.query.file;


	// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


	// console.log('API Request Body', op, JSON.stringify(req.body, null, 2));
	// console.log('API Request Query', op, JSON.stringify(req.query, null, 2));
	// console.log('-----------------------------------------------------------');

	function errorHandler(error = 'Invalid or missing parameters') {
		res.status(200).json({ status: 'error', message: error });
	}

	function successHandler(data) {
		res.status(200).json({ status: 'success', data: data });
	}


	// ====================================================================================================

	let data = null;
	let api_result = {};

	switch (op) {

		case 'Logger':

			if (!event || !token) { errorHandler(); return; }

			try {

				const event_query = `INSERT INTO logs (event, token, id1, id2, date) VALUES (?, ?, ?, ?, ?)`;
				const event_params = [event, token, id1, id2, now];
				const event_result = await post(event_query, event_params);

				if (event_result.affectedRows > 0) {
					successHandler();
				} else {
					errorHandler('An error occured while logging the event');
				}

			} catch (err) {
				errorHandler(err.message);
			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'ValidateToken':

			if (!jwt_token) {
				errorHandler('Token is missing');
				return;
			}

			try {

				const decoded = jwt.verify(jwt_token, jwtSecret);
				res.json({
					status: 'success',
					data: decoded,
					message: 'JWT Token is valid'
				});


			} catch (err) {

				res.json({
					status: 'error',
					message: err.message
				});

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'AssignNewTestVersion':

			if (!test_type || !token || !email || !company_id) {
				api_result = { status: 'error', message: 'Invalid or missing parameters [test_type, token, email or company_id]' };
			}

			let companyException = company_id == 134 ? "AND type = 2" : '';

			try {

				const sql1 = `SELECT version FROM version WHERE bolum = ${test_type} AND pasif IS NULL ${companyException} ORDER BY tarih, version ASC`;
				let AvailableTestVersions = await post(sql1);


				if (AvailableTestVersions.length === 0) {

					api_result = {
						status: 'error',
						message: 'No available test version found'
					};

				} else {

					const DefaultTestVersion = AvailableTestVersions[0].version;

					try {

						const sql2 = `SELECT 
											a.token,
											a.kurum AS company_id,
											a.testid AS test_version,
											CONCAT(b.ad, ' ', b.soyad) AS candidate,
											IFNULL(bitis2, bitis) AS complete_date
										FROM 
											oturum a
										LEFT JOIN
											adaylar b ON a.userid = b.id
										WHERE 
											a.mail = '${email}' AND
											a.sinav = ${test_type} AND
											a.testid > 0
										ORDER BY 
											complete_date, 
											a.aktif, 
											a.id
										DESC
										LIMIT 1`;

						let check_latest_version = await post(sql2);
						let newVersion;

						if (check_latest_version.length > 0) {

							let latest_version = check_latest_version[0].test_version;
							newVersion = latest_version + 1;

							if (!AvailableTestVersions.includes(newVersion)) {
								newVersion = DefaultTestVersion;
							}

						} else {
							newVersion = DefaultTestVersion;
						}

						try {

							console.log('New Version', newVersion);

							const sql3 = `UPDATE oturum SET testid = ${newVersion}  WHERE token = '${token}'`;
							const nv_updating = await post(sql3);

							if (nv_updating.affectedRows > 0) {

								api_result = {
									status: 'success',
									message: 'Test version assigned successfully',
									data: {
										token: token,
										test_type: test_type,
										new_test_version: newVersion ?? null
									}
								};

							} else {

								api_result = {
									status: 'error',
									message: 'An error occured while assigning the new test version'
								};

							}


						} catch (err) {

							api_result = {
								status: 'error',
								message: 'An error occured while assigning the new test version'
							};
						}


					} catch (err) {

						api_result = {
							status: 'error',
							message: err.message
						};

					}

				}


			} catch (err) {

				api_result = {
					status: 'error',
					message: err.message
				};

			}

			res.json(api_result);

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetCandidateTestInfoWithToken':

			if (!token || !email) { errorHandler(); return; }

			api_result = { token: token };

			try {

				const query = `SELECT 
								o.id,
								o.token,
								a.id AS user_id,
								a.ad AS name,
								a.soyad AS lastname,
								o.mail AS email,
								o.kurum AS test_issuer,
								c.kurum AS company,
								c.logo AS logo,
								o.lang AS lang,
								o.overall_group AS overall_group,
								o.sinav AS test_type,
								o.debug AS debug,
								o.do_not_allocate,
								(CASE WHEN o.sinav = 1 AND o.bitti = 1 THEN 2 ELSE o.sinav END) AS start_test_type,
								(CASE WHEN o.sinav = 1 AND o.bitti = 1 THEN o.soru2 ELSE o.soru END) AS question,
								(CASE WHEN o.sinav = 1 AND o.bitti = 1 THEN o.sure2 ELSE o.sure END) AS time,
								o.webcam AS photo,
								o.testid AS test_version,
								o.webcam_off AS offline_proctoring,
								o.fullscreen_off AS fullscreen_protection,
								IFNULL(o.first_login, NULL) AS first_login
							FROM
								oturum o
							LEFT JOIN
								adaylar a 
								ON o.userid = a.id
							LEFT JOIN 
								kurumlar c 
								ON o.kurum = c.id
							WHERE
								o.mail = ? AND
								o.token = ?
							LIMIT 1`;

				const stmt = await post(query, [email, token]);

				if (stmt.length === 0) {
					errorHandler('Invalid email or token');

				} else {

					const row = stmt[0];

					const t_user_id = row.user_id == 0 ? null : row.user_id;
					const t_overall_group = row.overall_group == 0 ? null : row.overall_group;
					const t_lang = row.lang ?? null;
					const t_question = row.question == 0 ? null : row.question;

					if (!t_user_id || t_overall_group) {

						// check user's other tests with the overall group
						const query2 = `SELECT
										x1.userid AS user_id,
										x2.ad AS name,
										x2.soyad AS lastname,
										x1.lang,
										IFNULL(x1.bitis2, x1.bitis) AS complete_date
									FROM
										oturum x1
									LEFT JOIN
										adaylar x2 ON x1.userid = x2.id
									WHERE
										x1.mail = ? AND
										x1.overall_group = ? AND
										x1.userid > 0
									ORDER BY
										complete_date
									DESC
									LIMIT 1`;

						const stmt2 = await post(query2, [email, t_overall_group]);

						if (stmt2.length > 0) {

							const row2 = stmt2[0];

							const ogd_user_id = row2.user_id == 0 ? null : row2.user_id;
							const ogd_name = row2.name ?? null;
							const ogd_lastname = row2.lastname ?? null;
							const ogd_lang = row2.lang ?? null;

							row.overall_group_data = {};
							row.overall_group_data.user_id = ogd_user_id;
							row.overall_group_data.name = ogd_name;
							row.overall_group_data.lastname = ogd_lastname;

							if (!t_lang)
								row.overall_group_data.lang = ogd_lang;

							// update user id on the token
							let query3 = `UPDATE oturum SET userid = ? WHERE token = ?`;
							await post(query3, [ogd_user_id, token]);
							row.overall_group_data.user_id_updated = true;

						}

					}

					api_result.status = 'success';
					api_result.data = row;
					api_result.message = 'Candidate test info retrieved successfully';

					// Update online status and server info
					if (!dev) {
						const query = `UPDATE oturum SET aktif = ?, server = 2 WHERE token = ?`;
						await post(query, [now, token]);
						api_result.active_status_updated = true;
					}

				}

			} catch (err) {

				api_result.status = 'error';
				api_result.message = err.message;

			}

			res.json(api_result);
			break;

		// --------------------------------------------------------------------------------------------

		case 'GetFirstDuration':

			if (!test_type) { errorHandler(); return; }

			try {

				if (test_type >= 3) {

					const sqlCol = test_type == 3 ? 'spsoru' : 'spcevap';

					try {
						data = await post(`SELECT ${sqlCol} AS time FROM sorular WHERE bolum = ? ORDER BY sira ASC LIMIT 1`, [test_type]);

						if (data.length > 0) {
							successHandler(data[0]);

						} else {
							errorHandler('No duration found with the given test type');
						}

					} catch (err) {
						errorHandler(err.message);
					}


				} else {

					// Reading & Listening
					data = await post(`SELECT sure FROM seviye WHERE bolum = ? AND baraj = 1 LIMIT 1`, [test_type]);
					if (data.length > 0) {
						successHandler(data[0]);

					} else {
						errorHandler('No duration found with the given test type');

					}
				}


			} catch (err) {
				errorHandler(err.message);
			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateFirstTestConfig':

			if (!token || !seconds || !test_type) { errorHandler(); return; }

			try {

				const timeCol = test_type == 2 ? 'sure2' : 'sure';
				const questionCol = test_type == 2 ? 'soru2' : 'soru';

				let query = `UPDATE oturum SET ${timeCol} = ?, ${questionCol} = 1 WHERE token = ?`;
				data = await post(query, [seconds, token]);

				if (data.affectedRows > 0) {
					successHandler();

				} else {
					errorHandler('An error occured while updating the test config');

				}

			} catch (err) {
				errorHandler(err.message);

			}

			break;

		//	--------------------------------------------------------------------------------------------

		case 'UpdateCandidateLanguage':

			if (!token) { errorHandler(); return; }

			try {

				data = await post(`UPDATE oturum SET lang = ? WHERE token = ?`, [lang, token]);
				if (data.affectedRows > 0) {
					successHandler();
				} else {
					errorHandler('An error occured while updating the language');
				}

			} catch (err) {
				errorHandler(err.message);

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateCandidateTestVersion':

			if (!token || !test_version) { errorHandler(); return; }

			try {

				data = await post(`UPDATE oturum SET testid = ? WHERE token = ?`, [test_version, token]);
				if (data.affectedRows > 0) {
					successHandler();
				} else {
					errorHandler('An error occured while updating the test version');
				}

			} catch (err) {

				errorHandler(err.message);

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetTermsAndConditions':

			if (!lang) { errorHandler(); return; }

			data = await post('SELECT * FROM terms_conditions WHERE lang = ? AND inactive = 0 ORDER BY id DESC LIMIT 1', [lang]);

			if (data.length === 0) {
				errorHandler('No Term & Condition found with the given language')
			} else {
				successHandler(data[0]);
			}

			break;
		// --------------------------------------------------------------------------------------------

		case 'CheckUserID':

			if (!email || !token) { errorHandler(); return; }

			data = await post(`SELECT
									(
										CASE
											
											WHEN a.overall_group > 0 THEN
											(
												CASE

													WHEN ( SELECT COUNT(*) FROM oturum WHERE mail = '${email}' AND overall_group = a.overall_group AND NOT token = '${token}' AND userid > 0 ) > 0 THEN
													0 ELSE 1 
												END 
												) ELSE 1 
											END 
											) AS show_info_form,
											(
												SELECT
													CASE
														WHEN COUNT(*) > 0 THEN 0 ELSE 1
													END
												FROM oturum o
												WHERE o.mail = '${email}' 
													AND o.userid IS NOT NULL
													AND (
														(o.aktif >= NOW() - INTERVAL 3 MONTH AND o.aktif IS NOT NULL) OR 
														(o.aktif IS NULL AND o.assign_date >= NOW() - INTERVAL 3 MONTH)
													)
											) AS show_survey_form
								FROM oturum a
								WHERE a.mail = '${email}'
								LIMIT 1`
			);

			successHandler(data.length > 0 ? data[0] : null);

			break;

		// --------------------------------------------------------------------------------------------

		case 'RegisterCandidate':

			let q1, q2, q3;

			if (!token || !name || !lastname || !date_of_birth || !gender) {
				errorHandler('Invalid or missing parameters [name, lastname, gender or date_of_birth]');
				return;
			}

			api_result = { dev: dev, token: token };

			try {

				let query1 = await post(`INSERT INTO adaylar SET ?`, {
					ad: name,
					soyad: lastname,
					tck: tckn,
					dtarih: moment(date_of_birth, 'DD/MM/YYYY').format('YYYY-MM-DD'),
					cinsiyet: gender,
					nationality: nationality ? nationality.toLowerCase() : null,
					mother_language: mother_language,
					education: education,
					dep: department,
					tel: `${area_code ? area_code : ''}${phone ? phone : ''}`,
					ktarih: moment().format('YYYY-MM-DD HH:mm:ss'),
					tckn_verification: tckn_verification
				});

				const InsertedID = query1.insertId ?? null;

				if (InsertedID) {

					// Update the user_id in the oturum table
					if (!dev) {
						q3 = await post(`UPDATE oturum SET userid = ? WHERE token = ?`, [InsertedID, token]);
						api_result.userid_updating = (q3.affectedRows > 0 ? true : false);

					} else {
						api_result.userid_updating = false
					}

					api_result.status = 'success';
					api_result.user_id = InsertedID
					api_result.message = 'Candidate registered successfully';

				} else {

					api_result.status = 'error';
					api_result.message = 'An error occured while registering the candidate to the adaylar table';

				}

			} catch (error) {

				api_result.status = 'error';
				api_result.message = error;

			}

			res.json(api_result);

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetSurveyData':

			const surveyArray = [];

			try {
				const sql = "SELECT * FROM exam_survey_question WHERE inactive = 0 ORDER BY rank";
				const stmt = await post(sql);

				if (stmt.length > 0) {

					for (const row of stmt) {

						const questionId = row.id;
						const sql2 = "SELECT * FROM exam_survey_answers WHERE question_id = ?";
						const answers = await post(sql2, [questionId]);

						row['answers'] = answers;
						surveyArray.push(row);
					}

					successHandler({ status: 'success', data: surveyArray });

				} else {
					errorHandler('No Exam Survey Questions found');
				}

			} catch (e) {
				errorHandler({ status: 'error', message: e.message });
			}

			break;


		// --------------------------------------------------------------------------------------------

		case 'InsertSurvey':

			if (!token) { errorHandler(); return; }

			// Insert Survey Answers
			const surveyAnswers = [];
			for (const key in req.body) {
				if (key.startsWith('answer_')) {

					const question_id = key.replace('answer_', '');
					const answer_id = req.body[key];

					if (answer_id) {
						surveyAnswers.push([token, question_id, answer_id]);
					}

				}
			}

			if (surveyAnswers.length > 0) {

				api_result.survey_form = {}

				try {

					const sql = "INSERT INTO exam_user_survey_answers (token, question, answer) VALUES ?";
					const stmt = await post(sql, [surveyAnswers]);

					if (stmt.affectedRows > 0) {

						api_result.survey_form.status = 'success';
						api_result.survey_form.message = 'Survey answers inserted successfully';
						api_result.survey_form.data = surveyAnswers;

					} else {
						api_result.survey_form.status = 'error';
						api_result.survey_form.message = 'An error occured while inserting the survey answers';

					}

				} catch (e) {

					api_result.survey_form.status = 'error';
					api_result.survey_form.message = e.message;

				}

			}

			// update info form 

			if (job_position || test_take_reason) {

				api_result.info_form = {}

				try {

					const sql2 = `UPDATE oturum SET ? WHERE token = ?`;
					const stmt2 = await post(sql2, [{
						job_position: job_position,
						test_take_reason: test_take_reason
					}, token]);

					if (stmt2.affectedRows > 0) {

						api_result.info_form.status = 'success';
						api_result.info_form.message = 'Candidate test info updated successfully';

					}

				} catch (e) {

					api_result.info_form.status = 'error';
					api_result.info_form.message = e.message;

				}



			}

			res.json(api_result);

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetTestInstruction':

			if (!test_type || !lang) { errorHandler(); return; }

			data = await post(`SELECT instruction FROM test_instructions WHERE test_type = ? AND lang = ? AND inactive = 0 ORDER BY id DESC LIMIT 1`, [test_type, lang]);

			if (data.length === 0) {
				errorHandler('No Test Instruction found with the given language')
			} else {
				successHandler(data[0]);
			}

			break;

		// --------------------------------------------------------------------------------------------
		// TODO: This timetable works based on single test version system. It should be updated to work with multiple test versions.

		case 'GetTestTimetable':

			if (!test_type || !test_version) { errorHandler(); return; }

			let query = `
					SELECT
						sgrup AS question_group,
						IF(a.bolum <> '4',
							CONCAT((SELECT sira FROM sorular WHERE bolum = a.bolum AND sgrup = a.sgrup ORDER BY sira ASC LIMIT 1),
							' - ',
							(SELECT sira FROM sorular WHERE bolum = a.bolum AND sgrup = a.sgrup ORDER BY sira DESC LIMIT 1)),
							''
						) AS question_range,
						IF(a.bolum IN (1,2),
							(SELECT sure FROM seviye WHERE bolum = a.bolum AND baraj = a.seviye LIMIT 1),
							IF(a.bolum = 3,
								a.spsoru,
								a.spcevap
							)
						) AS level_duration
					FROM
						sorular a
					WHERE
						a.bolum = '${test_type}'
						AND a.grupid = '${test_version}'
						${test_type == 3 ? "" : "AND a.sgrup > 0 "}
					GROUP BY
						a.${test_type == 3 ? "sgrup" : "seviye"}
					ORDER BY
						a.sira
			`;

			data = await post(query);

			if (data.length === 0) {
				errorHandler('No Test Timetable found with the given language')
			} else {
				successHandler(data);
			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetTestStartingInstructions':

			if (!lang) { errorHandler(); return; }

			data = await post(`SELECT title, subtitle, list, goodluck FROM test_starting_instructions WHERE lang = ? LIMIT 1`, [lang]);

			if (data.length === 0) {
				errorHandler('No Test Starting Instructions found with the given language')
			} else {
				successHandler(data[0]);
			}

			break;


		// --------------------------------------------------------------------------------------------

		case 'TCKNValidation':

			if (!tckn || !name || !lastname || !year) { errorHandler(); return; }

			const TCKNUrl = "https://tckimlik.nvi.gov.tr/Service/KPSPublic.asmx?WSDL"

			const TCKNParams = {
				"TCKimlikNo": tckn,
				"Ad": name,
				"Soyad": lastname,
				"DogumYili": year
			}

			soap.createClient(TCKNUrl, function (err, client) {
				client.TCKimlikNoDogrula(TCKNParams, function (err, result) {
					res.json({ status: 'success', data: result });
				});
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'UploadCandidatePhoto':

			if (!rq_token || !rq_fileName) { errorHandler(); return; }

			let response = { token: rq_token };
			const url = `https://storage.bunnycdn.com/${storageZone}/${rq_token}/Avatar/${rq_fileName}`;

			response.url = url;
			response.file_name = rq_fileName;

			const options = {
				method: 'PUT',
				body: buffer,
				headers: {
					'AccessKey': bunnycdnApiKey,
					'Content-Type': 'application/octet-stream',
				},
			};

			try {
				const res = await fetch(url, options);
				const json = await res.json();

				response.api_response = json;

				if (res.ok) {

					response.status = 'success';
					response.message = 'Photo uploaded successfully';

					// update the candidate photo url in the database (both photo and webcam fields for compatibility)
					data = await post(`UPDATE oturum SET photo = ?, webcam = ? WHERE token = ?`, [rq_fileName, rq_fileName, rq_token]);
					response.photo_updated = (data.affectedRows > 0 ? true : false)

				} else {

					response.status = 'error';
					response.message = 'An error occured while uploading the photo to BunnyCDN';
				}

			} catch (err) {

				response.status = 'error';
				response.message = err.message;
			}

			res.json(response);

			break;

		// --------------------------------------------------------------------------------------------

		case 'UploadSecurityVideo':

			if (!rq_token || !rq_fileName) { errorHandler(); return; }

			api_result = { token: rq_token };
			const UploadURL = `https://storage.bunnycdn.com/${storageZone}/${rq_token}/Webcam/${rq_fileName}`;


			api_result.url = UploadURL;
			api_result.file_name = rq_fileName;

			const uploadOptions = {
				method: 'PUT',
				body: buffer,
				headers: {
					'AccessKey': bunnycdnApiKey,
					'Content-Type': 'application/octet-stream',
				},
			};

			try {

				const res = await fetch(UploadURL, uploadOptions);
				const json = await res.json();

				api_result.api_response = json;

				if (res.ok) {

					api_result.status = 'success';
					api_result.message = 'Video uploaded successfully';

				} else {

					api_result.status = 'error';
					api_result.message = 'An error occured while uploading the video to BunnyCDN';

				}

			} catch (err) {

				api_result.status = 'error';
				api_result.message = err.message;

			}

			res.json(api_result);

			break;

		// --------------------------------------------------------------------------------------------

		case 'MobeseRecordOps':

			if (!token || !db_event) {
				errorHandler();
				return;
			}

			let message = "";
			let status = 'error';
			let old_records = {};

			try {

				if (db_event === 'insert') {

					const deleteStmt = `DELETE FROM mobese WHERE token = ?`;
					const deleteResult = await post(deleteStmt, [token]);
					const deleteResultAffectedRows = deleteResult.affectedRows;

					if (deleteResultAffectedRows > 0) {
						old_records = {
							status: 'success',
							message: `${deleteResultAffectedRows > 0 ? `${deleteResultAffectedRows} old Mobese records deleted successfully` : `No old Mobese records found`}`
						};
					}

					const insertStmt = `INSERT INTO mobese SET ?`;
					const insertResult = await post(insertStmt, [{ token, video: file, start: start_date }]);

					if (insertResult.affectedRows > 0) {
						status = 'success';
						message = 'Mobese record inserted successfully';
					}

				} else if (db_event === 'delete') {

					const sql3 = `DELETE FROM mobese WHERE token = ?`;
					const stmt3 = await post(sql3, [token]);
					const affectedRows = stmt3.affectedRows;

					status = 'success';
					message = `${affectedRows > 0 ? `${affectedRows} Mobese records deleted successfully` : `No Mobese records found`}`;

				}

			} catch (err) {
				message = err.message;
			}

			res.json({ status, message, data: { token, db_event, file, start_date }, old_records });
			break;

		// --------------------------------------------------------------------------------------------

		case 'GetQuestionData':

			if (!token || !test_type || !test_version || !question) {
				api_result.status = 'error';
				api_result.message = 'Test type or version is missing';
				return;
			}


			// set available questions false by default
			api_result.available_questions = false;
			api_result.question = question;
			api_result.test_type = test_type;
			api_result.test_version = test_version;

			api_result = {
				status: 'error',
				question: question,
				test_type: test_type,
				test_version: test_version,
				available_questions: false
			}

			try {

				let question_data = null;

				let sql = `SELECT 
								a.sira AS rank,
								a.id AS question_id,
								a.grupid AS test_version,
								a.img1 AS image1,
								a.img2 AS image2,
								a.soru AS question,
								a.talimat AS instruction,
								a.aplay AS audio,
								a.spcevap AS writing_time,
								a.sgrup AS question_group,
								a.sorutipi AS question_type,
								a.seviye AS level,
								a.cevap AS ca_old,
								a.correct_answer AS ca,
								a.puan AS score,
								a.kutu1 AS box1,
								a.kutu2 AS box2,
								a.kutu3 AS box3,
								a.kutu4 AS box4,
								a.kutu5 AS box5,
								a.kutu6 AS box6,
								a.kutu7 AS box7,
								a.metin AS text,
								(
										SELECT COUNT(1)
										FROM sorular b
										WHERE b.bolum = a.bolum AND b.grupid = a.grupid
									) AS total_questions,
									(
										SELECT COUNT(1)
										FROM sorular b
										WHERE b.bolum = a.bolum AND b.grupid = a.grupid AND b.sgrup = a.sgrup
									) AS total_questions_in_group,
									CASE
										WHEN a.sira = (
											SELECT MAX(c.sira)
											FROM sorular c
											WHERE c.bolum = a.bolum AND c.grupid = a.grupid AND c.sgrup = a.sgrup
										) THEN 1
										ELSE 0
									END AS last_question_of_group,
									CASE WHEN a.bolum = 4 THEN (
											SELECT 
												yanit
											FROM
												yanitlar
											WHERE 
												token = '${token}' AND
												soru = ${question}
											ORDER BY 
												id DESC
											LIMIT 1
										)
										ELSE NULL
									END AS candidate_answer
								FROM
									sorular a
								WHERE
									a.bolum = ? AND
									a.grupid = ? AND
									a.sira = ?
								LIMIT 1`;

				data = await post(sql, [test_type, test_version, question]);

				if (data.length > 0) {

					const row = data[0];
					question_data = row;
					row.token = token;

					const question_id = row.question_id;
					const question_type = row.question_type;

					if (test_type == 1 || test_type == 2) {

						// Get answers
						question_data.answers = {};

						const sql = `SELECT sira AS rank, cevap AS answer FROM cevaplar WHERE soruid = ? ORDER BY sira ASC`;
						const stmt = await post(sql, [question_id]);

						if (stmt.length > 0) {
							question_data.answers = stmt;
						}

						if (question_type == 4) { // Drag & Drop

							// Get Boxes
							question_data.boxes = [];

							const box_counts = 7;
							for (let i = 1; i <= box_counts; i++) {
								if (row[`box${i}`]) {
									question_data.boxes.push(row[`box${i}`]);
								}
							}
						}

					}

					api_result.status = 'success';
					api_result.data = question_data;
					api_result.message = 'Question data retrieved successfully';

				} else {

					api_result.query = sql;
					api_result.message = 'An error occured while retrieving question data';

				}

			} catch (err) {
				api_result.message = err.message;

			}

			res.json(api_result);

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetNextStep':

			// console.log('Token: ' + token + ' | Test Type: ' + test_type + ' | Test Version: ' + test_version + ' | Question ID: ' + question_id + ' | Question: ' + question + ' | Level: ' + level + ' | Next Level: ' + next_level);

			if (
				!token ||
				!test_type ||
				!test_version ||
				!question
			) {
				errorHandler();
				return;
			}

			// --------------------------------------------------------------------------------------------

			api_result = { status: 'error' }

			// --------------------------------
			api_result.form = {}
			const frm = api_result.form;
			frm.token = token;
			frm.test_type = test_type;
			frm.test_version = test_version;
			frm.question = question;
			// --------------------------------

			const rslt = api_result.result; // Shorten the result object
			api_result.result = {};

			if (test_type == 1 || test_type == 2) {

				if (!final_question) {

					console.log('Reading & Listening Test');
					// HANDLE READING & LISTENING TESTS

					// =========================================================================================================================
					// Check candidate's fail status

					const check_fail_status = await getFailStatus(token, test_type);
					const fail_status = check_fail_status.fail_status ?? null; // GET FAIL STATUS
					rslt.fail_status = fail_status;

					// =========================================================================================================================
					// TODO Sum total scores and compare the level threshold percentage to determine if the candidate can take the next level
					// const checkThreshold = CheckThresholdStatus();
					// if( fail_status == 0 or == 1 ){ }

					let questionQuery = '';

					if (next_level) {

						// READING OR LISTENING FOR THRESHOLDS, 
						// AND WRITING AND SPEAKING FOR EACH QUESTION

						api_result.next_level_called = true;

						// Check if there is a next level
						const check_next_level = await getNextLevel(test_type, level);
						const { status: next_level_status, message: next_level_message } = check_next_level;

						if (next_level_status) {

							const check_next_level_data = await getNextLevelData(test_type, level);
							const { status: new_level_data_status, message: new_level_data_message, next_level_data } = check_next_level_data;

							rslt.next_level_data = { status: new_level_data_status };

							if (new_level_data_status) {

								const { time: new_time, level: new_level } = next_level_data;

								Object.assign(rslt.next_level_data, { time: new_time, level: new_level });
								Object.assign(api_result, { new_time, new_level });

								questionQuery = `AND seviye = ${new_level}`;

								// Update time in the oturum table
								try {
									const timeCol = test_type == 2 ? 'sure2' : 'sure';
									const sql5 = `UPDATE oturum SET ${timeCol} = ? WHERE token = ?`;
									const query5 = await post(sql5, [new_time, token]);

									rslt.time_updated = query5.affectedRows > 0;

								} catch (err) {
									Object.assign(rslt, { time_updated: false, time_updated_message: err.message });
								}

							} else {
								rslt.new_level_message = new_level_data_message;
							}

						} else {
							rslt.new_level_message = next_level_message;
						}


					} else {

						// ====================================================================================================
						// Check Is there next question?
						questionQuery = `AND seviye = ${level} AND sira > ${question}`;

					}

					// ====================================================================================================
					// GET NEW QUESTION NUMBER

					if (questionQuery) {


						const check_new_question = await getNewQuestion(test_type, test_version, questionQuery);
						const new_question_status = check_new_question.status; // GET NEW QUESTION NUMBER

						rslt.new_question = {};
						if (new_question_status) {

							rslt.new_question.status = true; // Set new question status to success

							// ====================================================================================================
							// Update SQL

							rslt.new_question_update = {};

							try {

								const questionCol = test_type == 2 ? 'soru2' : 'soru';
								const new_question = check_new_question.new_question;

								const sql4 = `UPDATE oturum SET ${questionCol} = ? WHERE token = ?`;
								const query4 = await post(sql4, [new_question, token]);

								rslt.new_question_update.status = false; // Set new question update status to error by default

								if (query4.affectedRows > 0) {

									rslt.new_question_update.status = true; // Set new question update status to success

									// Final Result
									api_result.status = 'success';
									api_result.new_question = new_question; // Get Level's new question

								} else {
									rslt.new_question_update.message = 'An error occured while updating the test info';
								}

							} catch (err) {
								rslt.new_question_update.status = false;
								rslt.new_question_update.message = err.message;
							}

							// ====================================================================================================


						} else {
							rslt.new_question.status = false;
							rslt.new_question.message = check_new_question.message;
						}

					} else {
						api_result.result = null
						api_result.status = 'error';
						api_result.message = 'Question Query is missing';
					}


				}


			} else if (test_type >= 3) { // HANDLE SPEAKING AND WRITING TESTS


				// ====================================================================================================
				// SAVE ANSWER TO THE DATABASE

				const answer_arr = api_result.answer = { status: 'error' };

				// Save to answer
				if (answer?.length == 0 || !question_id) {
					answer_arr.message = 'Answer or Question ID is missing';

				} else {

					// Insert the answer
					try {

						const sql = `INSERT INTO yanitlar SET ?`;
						const query = await post(sql, {
							token: token,
							bolum: test_type,
							testid: test_version,
							soru: question,
							soruid: question_id,
							yanit: answer,
							tarih: now
						});

						if (query.affectedRows > 0) {
							api_result.status = 'success';
							answer_arr.answer_saved = true;
							answer_arr.db_event = 'insert';
						}

					} catch (err) {
						answer_arr.message = err.message;
					}

				}

				// END OF SAVE ANSWER TO THE DATABASE
				// ====================================================================================================

				if (!final_question) {

					console.log('Speaking & Writing Test');

					const sqlCol = test_type == 3 ? 'spsoru' : 'spcevap';

					try {

						const sql1 = `SELECT
							${sqlCol} AS time
						FROM
							sorular
						WHERE
							bolum = ? AND
							sira > ? AND
							grupid = ?
							
						ORDER BY
							sira ASC
						LIMIT 1`;
						const query1 = await post(sql1, [test_type, question, test_version]);

						if (query1.length > 0) {

							const row = query1[0];
							const new_time = row.time;
							const new_question = question + 1;

							api_result.status = 'success';
							api_result.new_question = new_question; // New question number
							api_result.new_time = new_time; // New time

							// UPDATE TIME ON THE OTURUM TABLE
							const update_question_and_time = await updateQuestionAndTime(token, new_question, new_time);
							const update_question_and_time_status = update_question_and_time.status;
							api_result.result.time_updated = update_question_and_time_status || false;

						} else {
							api_result.message = 'No next question and time found';
						}

					} catch (err) {
						api_result.message = err.message;
					}

				}

			}

			// --------------------------------------------------------------------------------------------
			// 	//TODO: Switch to system test level times based on the test version


			if (final_question) {
				api_result.status = 'success';
				api_result.message = 'Final question reached';
			}


			res.json(api_result);
			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateTimer':

			if (!token || !seconds || !test_type) { errorHandler(); return; }
			const flag_count = flag ?? null;

			try {

				const column = (test_type == 2 ? 'sure2' : 'sure');
				let sql = `UPDATE oturum SET 
									${column} = ?, 
									aktif = ?,
									flag = ?,
									blocked_time = ?
										WHERE
											token = ?`;
				data = await post(sql, [seconds, now, flag_count, blocked_time, token]);

				if (data.affectedRows > 0) {
					successHandler();
				} else {
					errorHandler('An error occured while updating the timer');
				}

			} catch (err) {
				errorHandler(err.message);

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateFirstLogin':

			if (!token) { errorHandler(); return; }

			try {

				data = await post(`UPDATE oturum SET first_login = ? WHERE token = ?`, [now, token]);
				if (data.affectedRows > 0) {
					successHandler();
				} else {
					errorHandler('An error occured while updating the first login');
				}

			} catch (err) {
				errorHandler(err.message);

			}

			break;
		// --------------------------------------------------------------------------------------------

		case 'InsertTestFeedback':

			if (!token || !stars) { errorHandler(); return; }

			try {

				data = await post(`SELECT COUNT(*) AS check_feedback FROM test_feedbacks WHERE token = ?`, [token]);
				const check_feedback = data[0].check_feedback;

				if (check_feedback > 0) {

					// update the feedback
					try {


						data = await post(`UPDATE test_feedbacks SET stars = ? WHERE token = ?`, [stars, token]);

						if (data.affectedRows > 0) {
							successHandler();

						} else {
							errorHandler('An error occured while updating the feedback');
						}

					} catch (err) {
						errorHandler(err.message);
					}

				} else {

					try {

						// insert the feedback
						data = await post(`INSERT INTO test_feedbacks SET token = ?, stars = ?`, [token, stars]);

						if (data.affectedRows > 0) {
							successHandler();
						} else {
							errorHandler('An error occured while inserting the feedback');
						}

					} catch (err) {

						errorHandler(err.message);

					}

				}

			} catch (err) {
				errorHandler(err.message);

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateFeedbackMessage':

			if (!token || !feedback) { errorHandler(); return; }

			try {

				data = await post(`UPDATE test_feedbacks SET feedback = ? WHERE token = ?`, [feedback, token]);

				if (data.affectedRows > 0) {
					successHandler();
				} else {
					errorHandler('An error occured while updating the feedback message');
				}

			} catch (err) {
				errorHandler(err.message);

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetOtherTestCompletedStatus':

			api_result = { status: 'error' };

			if (!token || !test_type) {
				api_result.message = 'Token or Test Type is missing';
			}

			try {

				const otherTestCol = test_type == 1 ? 'bitti2' : 'bitti';
				const sql = `SELECT 
								${otherTestCol} AS completed
							FROM
								oturum
							WHERE
								token = ?
							LIMIT 1`;

				const stmt = await post(sql, [token]);

				const row = stmt[0];
				const otherTestCompleted = row.completed;

				api_result.status = 'success';
				api_result.test_completed = otherTestCompleted == 1;

			} catch (err) {
				api_result.message = err.message;

			}

			res.json(api_result);
			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateCompleteStates':

			api_result = { status: 'error' };

			if (!token || !test_type) {
				api_result.message = 'Token or Test Type is missing';
			}


			const testCompleter = async (token) => {
				// Set the test as test_complete = 1
				const sql = `UPDATE oturum SET test_complete = 1 WHERE token = ?`;
				const stmt = await post(sql, [token]);
				return stmt.affectedRows > 0;
			}

			try {

				// Check if te test is RL and both tests are completed
				let testCompletionState = false;

				if (test_type <= 3) {

					const otherTestCol = test_type == 1 ? 'bitti2' : 'bitti';
					const sql = `SELECT ${otherTestCol} AS completed FROM oturum WHERE token = ? LIMIT 1`;
					const stmt = await post(sql, [token]);

					const row = stmt[0];
					const otherTestCompleted = row.completed;

					if (otherTestCompleted == 1) {
						await testCompleter(token);
						testCompletionState = true;
					}

					// TODO: Insert/Update the test aggrevated score to the database (candidate_results) Table
					// product_type, token, score_1, score_2



				} else { // Writing, Speaking Tests
					await testCompleter(token);
					testCompletionState = true;
				}

				api_result.test_completed = testCompletionState;

				const completeCol = test_type == 2 ? 'bitti2' : 'bitti';
				const completeDateCol = test_type == 2 ? 'bitis2' : 'bitis';

				const sql3 = `UPDATE oturum 
										SET
										${completeCol} = ?, 
										${completeDateCol} = NOW()
									WHERE
										token = ?`;

				const stmt3 = await post(sql3, [1, token]);

				api_result.status = stmt3.affectedRows > 0 ? 'success' : 'error';
				api_result.message = stmt3.affectedRows > 0 ? 'Test completed successfully' : 'An error occured while updating the test complete status';

				const sendEmail = send_email(
					value1, // subject
					[{
						name: name,
						email: email
					}], // to
					null, // cc
					[
						{ name: 'British Side', email: '<EMAIL>' },
						{ name: 'RBT', email: '<EMAIL>' }
					], // bcc
					{
						name: 'CEPA Test',
						email: '<EMAIL>'
					}, // replyTo
					null, // html_content
					emailTemplateId, // template_id
					{
						'LOGO': logo,
						'VALUE1': value1,
						'VALUE2': '',
						'VALUE3': '',
						'VALUE4': value4,
						'VALUE5': value5,
						'VALUE6': value6,
						'VALUE7': value7,
						'VALUE8': '',
						'VALUE9': '',
						'VALUE10': '',
						'VALUE11': '',
						'VALUE12': '',
						'VALUE16': value16,
						'VALUE17': value17,
						'VALUE69': value69,
						'VALUE70': value70
					}, // params
					null // attachment
				);
				api_result.email = sendEmail;	

			} catch (err) {
				api_result.message = err.message;

			}

			res.json(api_result);
			break;

		// --------------------------------------------------------------------------------------------

		case 'AllocateTest2Assessor':

			api_result = { status: 'error' };

			if (!token || !test_type) {
				api_result.message = 'Token or Test Type is missing';
			}

			api_result.data = { token: token, test_type: test_type };

			try {

				const sql1 = "SELECT COUNT(*) AS rater_allocated FROM writingyorum WHERE token = ? AND rater > 0 LIMIT 1";
				const stmt1 = await post(sql1, [token]);
				const row1 = stmt1[0] ?? null;

				if (row1.rater_allocated == 0) {

					// Allocate the test to an assessor
					const sql2 = "SELECT rater AS assessor_id FROM writingyorum WHERE rater > 0 ORDER BY date, id DESC LIMIT 1";
					const stmt2 = await post(sql2, [token]);
					const row2 = stmt2[0] ?? null;

					if (row2.assessor_id) {

						const last_allocated_assessor = row2.assessor_id;
						api_result.last_assessor = last_allocated_assessor;

						// Get next assessor
						const sql3 = `SELECT 
										id AS new_assessor_id,
										ad AS assessor_name,
										mail AS assessor_email
									FROM 
										users 
									WHERE 
										id > ? AND
										allocation = 1 AND
										inactive = 0
									LIMIT 1`;

						const stmt3 = await post(sql3, [last_allocated_assessor]);
						const row3 = stmt3[0] ?? null;

						const new_assessor_id = row3.new_assessor_id ?? null;
						const new_assessor_name = row3.assessor_name ?? null;
						const new_assessor_email = row3.assessor_email ?? null;

						if (new_assessor_id && new_assessor_email) {

							api_result.new_assessor = new_assessor_id;

							// INSERT THE ALLOCATION
							const sql4 = `INSERT INTO writingyorum SET token = ?, rater = ?, exam = ?, date = ?`;
							const stmt4 = await post(sql4, [token, new_assessor_id, test_type, now]);

							api_result.status = stmt4.affectedRows > 0 ? 'success' : 'error';
							api_result.message = stmt4.affectedRows > 0 ? 'Test allocated to an assessor successfully' : 'An error occured while allocating the test to an assessor';

							// Send email to the assessor

							const sendEmail = send_email(
								value1, // subject
								[{
									name: new_assessor_name,
									email: new_assessor_email
								}], // to
								null, // cc
								[
									{ name: 'British Side', email: '<EMAIL>' },
									//{ name: 'RBT', email: '<EMAIL>' }
								], // bcc
								{
									name: 'CEPA Test',
									email: '<EMAIL>'
								}, // replyTo
								null, // html_content
								emailTemplateId, // template_id
								{
									'LOGO': '',
									'VALUE1': value1,
									'VALUE2': '',
									'VALUE3': '',
									'VALUE4': value4,
									'VALUE5': value5,
									'VALUE6': '',
									'VALUE7': value7,
									'VALUE8': value8,
									'VALUE9': value9,
									'VALUE10': '',
									'VALUE11': value11,
									'VALUE12': value12,
									'VALUE16': value16,
									'VALUE17': value17,
									'VALUE18': value18,
									'VALUE19': value19,
									'VALUE20': value20,
									'VALUE21': new_assessor_name, // Assessor Name
									'VALUE22': value22,
									'VALUE23': value23,
									'VALUE26': value26,
									'VALUE27': value27,
									'VALUE69': value69,
									'VALUE70': value70
								}, // params
								null // attachment
							);
							console.log(sendEmail);

						} else {
							api_result.message = 'No new assessor found to allocate the test';

						}

					} else {
						api_result.message = 'No old assessor found to allocate the test';

					}

				} else {

					api_result.status = 'success';
					api_result.message = 'Test already allocated to an assessor';

				}

			} catch (err) {
				api_result.message = err.message;

			}

			console.log(api_result);
			res.json(api_result);
			break;

		// --------------------------------------------------------------------------------------------

		case 'GetVersionList':

			if (!test_type) { errorHandler(); return; }

			try {

				let sql = `SELECT
								version,
								type
							FROM 
								version
							WHERE
								bolum = ? AND
								pasif IS NULL
							ORDER BY
								version ASC`;

				data = await post(sql, [test_type]);

				if (data.length > 0) {
					api_result.status = 'success';
					api_result.data = data;

				} else {
					api_result.status = 'error';

				}

			} catch (err) {

				api_result.status = 'error';
				api_result.message = err.message;

			}

			res.json(api_result);

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetQuestionList':

			if (!test_type || !test_version) { errorHandler(); return; }

			try {

				let sql = `SELECT
								sira AS rank,
								sorutipi AS question_type,
								sgrup AS question_group,
								seviye AS level
							FROM
								sorular
							WHERE
								bolum = ? AND
								grupid = ?
							ORDER BY
								sira ASC`;

				data = await post(sql, [test_type, test_version]);

				if (data.length > 0) {

					api_result.status = 'success';
					api_result.data = data;

				} else {
					api_result.status = 'error';

				}

			} catch (err) {

				api_result.status = 'error';
				api_result.message = err.message;

			}

			res.json(api_result);

			break;

		// --------------------------------------------------------------------------------------------

		case 'DevUpdate':

			if (!token || !column || !value) { errorHandler(); return; }

			api_result = {
				token: token,
				column: column,
				value: value,
				status: 'error',
				message: 'An error occured while updating the value'
			}

			try {

				data = await post(`UPDATE oturum SET ${column} = ${value} WHERE token = ?`, [token]);

				if (data.affectedRows > 0) {

					api_result.status = 'success';
					api_result.message = 'Value updated successfully';


				} else {
					api_result.status = 'error';

				}

			} catch (err) {
				api_result.status = 'error';

			}

			res.json(api_result);

			break;

		// --------------------------------------------------------------------------------------------

		default:
			errorHandler('Invalid API operation');
	}

	// ====================================================================================================

});

export default router;