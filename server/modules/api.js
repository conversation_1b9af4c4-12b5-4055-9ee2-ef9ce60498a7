import express from 'express';
const router = express.Router();
import jwt from 'jsonwebtoken';

import { jwtSecret, test_list } from './config.js';
import { newToken, BrevoConnect } from './functions.js';

import { request } from './mysql/mysql.js';
import { send_email } from './email.js';
import concatVideos from './webmWorker.js';
import { htmlToPdf } from './htmlToPdf.js';

// import { io } from './socket.js';

import { EventEmitter } from 'events';
const Bus = new EventEmitter();
Bus.setMaxListeners(500);

// ====================================================================================================

router.use('/:op', async (req, res) => {

	const op = req.params.op;

	let excluded_companies = 'NOT IN (1, 120, 192)';

	let query,
		query2,
		query3,
		date_query,
		term_query,
		test_type_query,
		assessor_query,
		company_query,
		keyword_query,
		result,
		result2,
		result3,
		totalCounts = 0,
		limit_query = '',
		final_query = '';

	// request.form data
	const {
		id,
		token,
		date,
		term,
		test_type,
		exclude_companies = 0,
		company_id,
		assessor_id,
		keyword,
		test_version,
		token_count,
		start_date,
		end_date,
		staff_id,
		seconds,
		online,
		page,
		limit,
		email,
		department,
		password,
		method,
		storage_zone,
		url_params
	} = req.body;

	if (page && limit) {

		const start = (page - 1) * limit;
		const end = start + limit;
		limit_query = `LIMIT ${start}, ${end}`;
		console.log('limit_query', limit_query);

	}

	// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

	switch (op) {

		// --------------------------------------------------------------------------------------------

		case 'CDNConnect':

			console.log('url_params', url_params);
			console.log('method', method);

			if (!url_params || !method) {
				res.json({ status: 'error', message: 'Missing parameters' });
				return;
			}

			CDNConnect(
				url_params,
				method
			).then((data) => {

				if (data.length == 0) {
					res.json({ status: 'error', message: 'No videos found' });
					return;
				}

				res.json({
					status: (data.length > 0 ? 'success' : 'error'),
					data: data
				});

			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetTransactionEmails':

			const data = await BrevoConnect(
				`/smtp/emails?email=${email}`
			);

			res.json(data);
			break;

		// --------------------------------------------------------------------------------------------

		case 'CASystemLogin':

			if (email == undefined || password == undefined) {
				res.json({ status: 'error', message: 'Please enter your email and password' });
				return;

			}

			query = `SELECT
						id,
						ad AS name,  
						title,
						photo
					FROM
						users
					WHERE
						mail = ?
						AND pass = ?
					LIMIT 1`;

			result = await request(query, [email, password]);

			if (result.length == 0) {
				res.json({ status: 'error', message: 'Invalid email or password' });
				return;

			}

			let row = result[0];

			if (row.inactive == 1) {
				res.json({ status: 'error', message: 'Your account is inactive' });
				return;

			}


			// Create a JWT
			const new_jwtToken = jwt.sign({ id: row.id, email: row.mail }, jwtSecret, {
				expiresIn: '3h'
			});


			res.json({
				status: 'success',
				id: row.id,
				token: new_jwtToken,
				name: row.name,
				email: email,
				title: row.title,
				photo: row.photo
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAUserOnline':

			query = `UPDATE users SET online = NOW() WHERE id = ?`;
			result = await request(query, [id]);
			res.json({ status: 'success' });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CACheckToken':

			if (token == undefined) {
				res.json({ status: 'error', message: 'Invalid token' });
				return;
			}

			try {
				const decoded = jwt.verify(token, jwtSecret);
				res.json({ status: 'success', data: decoded });

			} catch (error) {
				console.log('Invalid token');
				res.json({ status: 'error', message: 'Invalid token' });
			}


			break;


		// --------------------------------------------------------------------------------------------

		case 'CAHomeStatistics':

			query = `SELECT
						COUNT(*) AS completed_tests,
						(
						SELECT
							COUNT(*) 
						FROM
							oturum 
						WHERE
							demo = 0 
							AND ( ( sinav = 1 AND ( bitti IS NULL OR bitti2 IS NULL )) OR ( sinav >= 3 AND bitti IS NULL ) ) 
							AND assign_date >= DATE_SUB( CURRENT_DATE, INTERVAL 1 YEAR ) 
						) AS incomplete_tests,
						( SELECT COUNT( DISTINCT userid ) FROM oturum WHERE userid > 0 ) AS test_takers 
					FROM
						oturum 
					WHERE
						demo = 0 
						AND kurum NOT IN ( 1, 120, 192 ) 
						AND ((
								sinav = 1 
								AND bitti = 1 
								AND bitti2 = 1 
							) 
						OR ( sinav >= 3 AND bitti = 1 ));`;

			result = await request(query);
			res.json({ status: 'success', data: result[0] });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CANewToken':

			const newTokenList = [];
			const tokens = [];
			let success_tokens = 0;

			for (const test_type_id of test_type) {

				let values = [];

				for (let i = 0; i < token_count; i++) {

					let new_token;

					// Keep generating tokens until a truly unique token is found
					while (true) {
						new_token = newToken();

						if (!newTokenList.includes(new_token)) {

							// Check if the token exists in the database
							query = `SELECT token FROM oturum WHERE token = ?`;
							let existingToken = await request(query, new_token);

							if (!existingToken || existingToken.length === 0) {
								// The token doesn't exist in the database, so it's truly unique
								// console.log('Unique token found: ' + new_token);
								break;
							}
						}
					}

					let progress = ((i + 1) / token_count) * 100;

					// Add the unique token to the list
					newTokenList.push(new_token);

					// Prepare the values for this token
					let value = [new_token, test_type_id, company_id, id, 'NOW()'];
					values.push(value);

					tokens.push({
						token: new_token,
						test_type: test_type_id,
						staff_id: id
					});

					// Emit the event
					// round progress

					// console.log('Emitting progress: ' + progress.toFixed(0));
					// io.sockets.emit('progress', progress.toFixed(0));

				}

				// Try to perform a bulk insert
				try {
					query2 = `INSERT INTO oturum (token, sinav, kurum, user, tarih) VALUES ?`;
					result2 = await request(query2, [values]);

					success_tokens += result2.affectedRows;

				} catch (error) {
					console.error(error);
				}

			}

			res.json({
				status: 'success',
				data: tokens,
				message: `${success_tokens} tokens generated successfully`
			});

			break;


		// --------------------------------------------------------------------------------------------

		case 'CAActiveUsers':

			query = `SELECT
						id,
						ad AS name,
						online AS date_online,
						photo AS avatar,
						CASE 
							WHEN title LIKE '%Assessor%' THEN 2 
							ELSE 1 
						END AS user_type,
						NULL AS company_name
					FROM
						users 
					WHERE
						ONLINE > DATE_SUB( NOW(), INTERVAL 3 MINUTE )

					UNION ALL

					SELECT
						a.id,
						a.contact AS name,
						a.online AS date_online,
						a.avatar,
						3 AS user_type,
						b.kurum AS company_name
					FROM
						sub_company_logins a
						INNER JOIN kurumlar b ON a.company_id = b.id
					WHERE
						a.online > DATE_SUB( NOW(), INTERVAL 3 MINUTE )
						AND a.id NOT IN (13)

					ORDER BY
						date_online DESC
					`;

			result = await request(query);
			res.json({ status: 'success', data: result });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CADeadlineList':

			query = `SELECT
						d.date,
						IFNULL( o.awaiting_tests, 0 ) AS awaiting_tests 
					FROM
						(
						SELECT
							DATE( DATE_SUB( NOW(), INTERVAL DAYOFMONTH( NOW())- 1 DAY ) + INTERVAL m DAY ) AS date 
						FROM
							(
							SELECT
								@ROW := @ROW + 1 AS m 
							FROM
								( SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 ) t1,
								( SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 ) t2,
								( SELECT @ROW :=- 1 ) t3 
							) d 
						WHERE
							DATE( DATE_SUB( NOW(), INTERVAL DAYOFMONTH( NOW())- 1 DAY ) + INTERVAL m DAY ) < DATE( DATE_ADD( NOW(), INTERVAL 1-DAY ( NOW()) DAY ) + INTERVAL 1 MONTH ) 
						) d
						LEFT JOIN ( SELECT DATE( deadline ) AS date, COUNT(*) AS awaiting_tests FROM oturum GROUP BY DATE( deadline ) ) o ON d.date = o.date 
					ORDER BY
						d.date`;

			result = await request(query);
			res.json({ status: 'success', data: result });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAMobese':

			query = `SELECT
						a.token,
						CONCAT(c.ad, ' ', c.soyad) name,
						CASE
						WHEN b.sinav = 1 AND b.bitti IS NULL THEN 1 
						WHEN b.sinav = 1  AND b.bitti2 IS NULL THEN 2
						ELSE b.sinav END AS test_type,
						CASE
						WHEN b.sinav = 1 AND b.bitti IS NULL THEN b.soru
						WHEN b.sinav = 1 AND b.bitti2 IS NULL THEN b.soru2
						ELSE b.soru END AS question,
						a.start AS date,
						a.video,
						d.kurum AS company
					FROM
						mobese a 
					INNER JOIN 
						oturum b ON a.token = b.token
					INNER JOIN
						adaylar c ON b.userid = c.id
					INNER JOIN
						kurumlar d ON b.kurum = d.id
					WHERE
						a.start > DATE_SUB( NOW(), INTERVAL 3 MINUTE )
					ORDER BY
						a.start DESC`;

			result = await request(query);
			res.json({ status: 'success', data: result });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CANavbarStats':

			query = `SELECT 
						(SELECT COUNT(*) FROM mobese WHERE START > DATE_SUB(NOW(), INTERVAL 3 MINUTE)) mobese_count,
						(SELECT COUNT(*) 
							FROM writingyorum
							INNER JOIN oturum ON writingyorum.token = oturum.token
							WHERE 
								writingyorum.complete IS NULL
								AND oturum.demo = 0
								AND oturum.test_complete = 1
								AND oturum.sinav >= 3
							) AS awaiting_assessments_count,
						(SELECT COUNT(*) FROM oturum WHERE aktif > DATE_SUB( NOW(), INTERVAL 3 MINUTE )) AS active_candidates_count,
						(SELECT COUNT(*) FROM oturum a
						LEFT JOIN security_checks b ON a.token = b.token 
					WHERE 
						a.demo = 0 
						AND a.test_complete = 1 
						AND b.status IS NULL 
						AND IFNULL( a.bitis2, a.bitis ) >= DATE( NOW() - INTERVAL 90 DAY ) 
						AND (a.sinav < 3 OR ( a.sinav >= 3 
						AND EXISTS ( SELECT 1 FROM writingyorum wy WHERE wy.token = a.token AND wy.complete = 1 )))) AS awaiting_security_checks_count
`;
			res.json(await request(query));

			break;

		// --------------------------------------------------------------------------------------------

		case 'CALiveTestTracking':

			query = `SELECT
						a.id,
						a.token,
						a.testid AS test_version,
						CASE
						WHEN a.sinav = 1 AND a.bitti IS NULL THEN 1 
						WHEN a.sinav = 1  AND a.bitti2 IS NULL THEN 2
						ELSE a.sinav END AS test_type,
						CASE
							WHEN a.sinav = 1 AND a.bitti IS NULL THEN a.soru
							WHEN a.sinav = 1 AND a.bitti2 IS NULL THEN a.soru2
							ELSE a.soru END AS question,
						CASE
							WHEN a.sinav = 1 AND a.bitti IS NULL THEN a.sure
							WHEN a.sinav = 1 AND a.bitti2 IS NULL THEN a.sure2
							ELSE a.sure END AS time,
						CONCAT( b.ad, ' ', b.soyad ) AS candidate,
						c.kurum AS company,
						d.video,
						d.start AS video_date,
						a.aktif AS date_online
					FROM
						oturum a
						INNER JOIN adaylar b ON a.userid = b.id
						INNER JOIN kurumlar c ON a.kurum = c.id
						LEFT JOIN mobese d ON a.token = d.token
					WHERE
						a.aktif > DATE_SUB( NOW(), INTERVAL 3 MINUTE ) 
				ORDER BY
				aktif DESC`;

			result = await request(query);
			res.json({ status: 'success', data: result });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAHomeIncompleteTests':

			query = `SELECT
						a.token,
						CASE
							WHEN a.sinav = 1 AND a.bitti IS NULL THEN 1
							WHEN a.sinav = 1 AND a.bitti2 IS NULL THEN 2
							ELSE a.sinav
						END AS test_type,
						CONCAT( b.ad, ' ', b.soyad ) AS test_taker,
						a.aktif AS last_event,
						c.kurum AS company_name,
						c.logo AS company_logo 
					FROM
						oturum a
						INNER JOIN adaylar b ON a.userid = b.id
						LEFT JOIN kurumlar c ON a.kurum = c.id 
					WHERE
						a.test_complete = 0
						AND a.userid > 0
						AND a.aktif BETWEEN NOW() - INTERVAL 3 DAY AND NOW() - INTERVAL 30 MINUTE
					ORDER BY
						a.aktif DESC`;

			result = await request(query);
			res.json({ status: 'success', data: result });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAHomeCompleteTests':

			query = `SELECT
						a.token,
						a.sinav AS test_type,
						CONCAT( b.ad, ' ', b.soyad ) AS test_taker,
						c.kurum AS company_name,
						c.logo AS company_logo,
						IFNULL(a.bitis2, a.bitis) AS last_event
					FROM
						oturum a
						INNER JOIN adaylar b ON a.userid = b.id
						LEFT JOIN kurumlar c ON a.kurum = c.id 
					WHERE
						a.test_complete = 1
						AND DATE(IFNULL(a.bitis2, a.bitis)) = '${date}'
					ORDER BY
						last_event DESC`;

			result = await request(query);
			res.json({ status: 'success', data: result });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAAuth':

			query = `SELECT
						ad AS name,
						mail AS email,
						title,
						photo
					FROM users
					WHERE
						id = ?
						AND inactive = 0
					LIMIT 1`;

			result = await request(query, [id]);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});


			break;
		// --------------------------------------------------------------------------------------------

		case 'CASearch':

			const search_keyword = req.query.q;
			const q_limit = req.query.limit ?? 10;

			query = `SELECT
						a.token,
						b.ad AS name,
						b.soyad AS lastname,
						c.kurum AS company_name
					FROM
						oturum a
					LEFT JOIN 
						adaylar b ON a.userid = b.id
					LEFT JOIN
						kurumlar c ON a.kurum = c.id
					WHERE
						a.token LIKE '%${search_keyword}%' OR
						b.ad LIKE '%${search_keyword}%' OR
						b.soyad LIKE '%${search_keyword}%'
					ORDER BY
						a.kurum
					LIMIT ${q_limit}`;


			result = await request(query);
			res.json({
				results: result
			});


			break;

		// --------------------------------------------------------------------------------------------

		case 'CAOfflineChecks':

			// check search data
			date_query = (date ?
				`DATE(IFNULL(a.bitis2, a.bitis)) = '${date}'` :
				`IFNULL( a.bitis2, a.bitis ) >= DATE( NOW() - INTERVAL 90 DAY )`
			);

			if (test_type == 1) {
				test_type_query = `a.sinav = 1`;

			} else if (test_type >= 3) {
				test_type_query = `a.sinav = ${test_type} AND EXISTS ( SELECT 1 FROM writingyorum wy WHERE wy.token = a.token AND wy.complete = 1 )`;

			} else {
				test_type_query = `( a.sinav < 3
									OR (
										a.sinav >= 3
										AND EXISTS (
											SELECT 1
											FROM writingyorum wy
											WHERE wy.token = a.token
											AND wy.complete = 1
										)
									)
								)`;
			}

			company_query = (company_id ?
				`a.kurum = ${company_id}` :
				`a.kurum ${excluded_companies}`
			);

			query = `SELECT
						a.id,
						a.token,
						a.sinav AS test_type,
						IFNULL( a.bitis2, a.bitis ) AS complete_date,
						c.ad AS staff_name,
						d.kurum AS company_name,
						a.test_category,
						a.app AS api,
						e.contact AS token_created_by
					FROM
						oturum a
						LEFT JOIN security_checks b ON a.token = b.token
						LEFT JOIN users c ON b.staff = c.id
						LEFT JOIN kurumlar d ON a.kurum = d.id
						LEFT JOIN sub_company_logins e ON a.user = e.id
					WHERE
						a.demo = 0 
						AND a.test_complete = 1
						AND b.status IS NULL
						AND ${company_query}
						AND ${date_query}
						AND ${test_type_query}
					ORDER BY
						CASE
							WHEN a.sinav = 3 THEN 0
							WHEN a.sinav = 4 THEN 1
							ELSE 2
						END,
						complete_date ASC
						`;

			totalCounts = await request(`SELECT COUNT(*) AS total FROM (${query}) AS t`);

			final_query = query + limit_query;
			result = await request(final_query);

			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result,
				totalCounts: totalCounts[0].total,
				query: final_query
			});

			break;

		//	--------------------------------------------------------------------------------------------

		case 'CAMobeseCandidateDetails':

			query = `SELECT
						CONCAT(a.ad, ' ', a.soyad) AS candidate_name,
						b.mail AS email
					FROM
						adaylar a
					INNER JOIN 
						oturum b ON a.id = b.userid
					WHERE
						b.token = ?
					LIMIT 1`;

			result = await request(query, [token]);
			let candidate_email = (result.length > 0 ? result[0].email : '')

			// get tests
			let tests = [];

			if (candidate_email !== '') {
				query2 = `SELECT
									a.token,
									b.sinav AS test_type,
									a.score_1,
									a.score_2,
									IFNULL(b.bitis2, b.bitis) AS complete_date
								FROM
									candidate_results a
									INNER JOIN oturum b ON a.token = b.token 
								WHERE
									b.mail = ?
									AND (IFNULL(b.bitis2, b.bitis) >= DATE_SUB(NOW(), INTERVAL 30 DAY))

								ORDER BY 
										complete_date`;

				result2 = await request(query2, [candidate_email]);
				tests = result2;
			}



			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				name: (result.length > 0 ? result[0].candidate_name : ''),
				email: (result.length > 0 ? result[0].email : ''),
				tests: tests
			});

			break;

		//	--------------------------------------------------------------------------------------------

		case 'CAOfflineCheckCompanyList':

			query = `SELECT
						c.id,
						c.kurum AS company_name,
						COUNT( o.token ) AS unchecked_tokens_count 
					FROM
						kurumlar c
						JOIN oturum o ON o.kurum = c.id 
					WHERE
						o.demo = 0 
						AND o.test_complete = 1 
						AND o.kurum ${excluded_companies}
						AND IFNULL( o.bitis2, o.bitis ) >= DATE( NOW() - INTERVAL 90 DAY ) 
						AND o.token NOT IN ( SELECT sc.token FROM security_checks sc ) 
					GROUP BY
						c.id,
						c.kurum 
					ORDER BY
						company_name`;

			result = await request(query);
			res.json({
				status: 'success',
				data: result
			});

			break;
		// --------------------------------------------------------------------------------------------

		case 'CAInvoiceList':

			company_query = (company_id ? ` = ${company_id}` : `${excluded_companies}`);


			query = `SELECT
						a.id,
						a.kurum AS company_name,
						COUNT(*) AS total_assigned_tokens,
						SUM( CASE WHEN b.sinav = 1 THEN 1 ELSE 0 END) AS rl,
						SUM( CASE WHEN b.sinav = 3 THEN 1 ELSE 0 END) AS sp,
						SUM( CASE WHEN b.sinav = 4 THEN 1 ELSE 0 END) AS wr,
						SUM( CASE WHEN b.sinav = 1 AND test_issuer_id > 0 THEN 1 ELSE 0 END) AS sub_rl,
						SUM( CASE WHEN b.sinav = 3 AND test_issuer_id > 0 THEN 1 ELSE 0 END) AS sub_sp,
						SUM( CASE WHEN b.sinav = 4 AND test_issuer_id > 0 THEN 1 ELSE 0 END) AS sub_wr,
						SUM( CASE WHEN b.sinav = 1 AND app > 0 THEN 1 ELSE 0 END) AS api_rl,
						SUM( CASE WHEN b.sinav = 3 AND app > 0 THEN 1 ELSE 0 END) AS api_sp,
						SUM( CASE WHEN b.sinav = 4 AND app > 0 THEN 1 ELSE 0 END) AS api_wr
					FROM
						kurumlar a
						INNER JOIN oturum b ON a.id = b.kurum
					WHERE
						a.id ${company_query}
						AND b.demo = 0
						AND (b.assign_date >= ? AND b.assign_date <= ?)
					GROUP BY
						a.id
					ORDER BY
						total_assigned_tokens DESC`;

			result = await request(query, [start_date, end_date]);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});

			break;

		// --------------------------------------------------------------------------------------------


		case 'CAMobeseStats':


			query = `SELECT
						COUNT(*) AS awaiting_checks,
						SUM(CASE WHEN DATE(IFNULL(a.bitis2, a.bitis)) = DATE(NOW()) THEN 1 ELSE 0 END) AS today_awaiting_checks
					FROM
						oturum a
					LEFT JOIN 
						security_checks b ON a.token = b.token
					WHERE
						a.test_complete = 1
						AND a.demo = 0
						AND b.status IS NULL
						AND a.kurum ${excluded_companies}
						AND IFNULL( a.bitis2, a.bitis ) >= DATE( NOW() - INTERVAL 90 DAY )
						`;

			try {
				result = await request(query);
				res.json({
					status: (result.length > 0 ? 'success' : 'error'),
					data: result
				});

			} catch (error) {
				console.error(error);
				res.json({
					status: 'error',
					message: 'An error occurred while executing the query.'
				});
			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'CACompanyList':

			query = `SELECT
						id,
						kurum AS company_name
					FROM
						kurumlar
						${exclude_companies == 1 ? `WHERE id ${excluded_companies}` : ''}
					ORDER BY
						company_name`;

			try {
				result = await request(query);
				res.json({
					status: (result.length > 0 ? 'success' : 'error'),
					data: result
				});

			} catch (error) {
				console.error(error);
				res.json({
					status: 'error',
					message: 'An error occurred while executing the query.'
				});

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'CALastTokenCreatedDate':

			query = `SELECT
						a.tarih AS date,
						b.ad AS staff_name,
						(SELECT COUNT(*) FROM oturum WHERE kurum = a.kurum AND LENGTH(mail) = 0 AND sinav = 1) AS reading_listening,
						(SELECT COUNT(*) FROM oturum WHERE kurum = a.kurum AND LENGTH(mail) = 0 AND sinav = 3) AS speaking,
						(SELECT COUNT(*) FROM oturum WHERE kurum = a.kurum AND LENGTH(mail) = 0 AND sinav = 4) AS writing
					FROM
						oturum a
						LEFT JOIN users b ON a.USER = b.id 
					WHERE
						a.kurum = ${company_id}
					ORDER BY
						a.tarih DESC 
						LIMIT 1`;

			result = await request(query);
			res.json({
				date: result[0].date,
				staff_name: result[0].staff_name,
				current_tokens: {
					1: result[0].reading_listening,
					3: result[0].speaking,
					4: result[0].writing
				}
			});

			console.log(result[0].date);

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAAssessmentsPageData':


			query = `SELECT
							COUNT(*) awaiting_assessments,
							( SELECT COUNT(*) FROM users WHERE title LIKE '%Assessor%' AND inactive = 0 ) AS total_assessors,
							( SELECT COUNT(*) FROM users WHERE title LIKE '%Assessor%' AND allocation = 1 AND inactive = 0 ) AS active_assessors,
							(
							SELECT
								COUNT(*) 
							FROM
								writingyorum x1
								INNER JOIN oturum x2 ON x1.token = x2.token 
							WHERE
								x1.complete IS NULL 
								AND x2.sinav = 3 
								AND x2.demo = 0 
								AND x2.test_complete = 1 
							) AS awaiting_speaking,
							(
							SELECT
								COUNT(*) 
							FROM
								writingyorum x1
								INNER JOIN oturum x2 ON x1.token = x2.token 
							WHERE
								x1.complete IS NULL 
								AND x2.sinav = 4 
								AND x2.demo = 0 
								AND x2.test_complete = 1 
							) AS awaiting_writing 
						FROM
							writingyorum a
							INNER JOIN oturum b ON a.token = b.token 
						WHERE
							a.complete IS NULL 
							AND b.sinav >= 3 
							AND b.demo = 0 
							AND b.test_complete = 1`;


			try {

				result = await request(query);
				let query_status = (result.length > 0 ? 'success' : 'error');


				let company_list = [];

				if (query_status === 'success') {

					// ----------------------------------------------------------------------------
					// Get Company List

					query2 = `SELECT
										c.id,
										c.kurum AS company_name,
										subquery.awaiting_assessments 
									FROM
										kurumlar c
										INNER JOIN (
										SELECT
											b.kurum,
											COUNT(*) AS awaiting_assessments 
										FROM
											writingyorum a
											INNER JOIN oturum b ON a.token = b.token 
										WHERE
											a.complete IS NULL 
											AND b.demo = 0 
										GROUP BY
											b.kurum 
										) subquery ON c.id = subquery.kurum 
									WHERE
										c.id ${excluded_companies}
									ORDER BY
										c.kurum`;

					result2 = await request(query2);
					company_list = (result2.length > 0 ? result2 : []);

					// add company_list to result
					result[0].company_list = company_list;

					// ----------------------------------------------------------------------------
					// Get Assessor List

					query3 = `SELECT
										b.id,
										b.ad AS name,
										COALESCE ( subquery.awaiting_assessments, 0 ) AS awaiting_assessments 
									FROM
										users b
										LEFT JOIN (
										SELECT
											a.rater,
											COUNT(*) AS awaiting_assessments 
										FROM
											writingyorum a
											INNER JOIN oturum c ON a.token = c.token 
										WHERE
											a.complete IS NULL 
											AND c.demo = 0 
											AND c.bitti = 1 
										GROUP BY
											a.rater 
										) subquery ON b.id = subquery.rater 
									WHERE
										b.inactive = 0 
										AND subquery.awaiting_assessments > 0 
									ORDER BY
										b.ad`;

					result3 = await request(query3);
					result[0].assessor_list = (result3.length > 0 ? result3 : []);

				}

				res.json({
					status: query_status,
					data: result
				});

			} catch (error) {

				console.error(error);
				res.json({
					status: 'error',
					message: 'An error occurred while executing the query. Error: ' + error
				});

			}

			break;
		// --------------------------------------------------------------------------------------------

		case 'CATests':


			keyword_query = (keyword !== undefined ? `AND ( CONCAT(c.ad, ' ', c.soyad) LIKE '%${keyword}%' OR a.token LIKE '%${keyword}%' OR a.mail LIKE '%${keyword}%' )` : '');

			date_query = (start_date !== undefined && end_date !== undefined ? `HAVING complete_date BETWEEN DATE('${start_date}') AND DATE('${end_date}')` : '');

			test_type_query = (test_type ? ` AND a.sinav = ${test_type}` : ``);

			company_query = (company_id ? ` AND a.kurum = ${company_id}` : ``);


			query = `SELECT
						a.id,
						a.token,
						CONCAT(c.ad, ' ', c.soyad) AS candidate,
						a.mail AS email,
						b.id AS company_id,
						b.kurum AS test_issuer,
						b.logo AS test_issuer_logo,
						a.sinav AS test_type,
						IFNULL(a.bitis2, a.bitis) AS complete_date,
						(
							CASE
							WHEN a.test_complete = 1 THEN 3
							WHEN a.aktif > 0 THEN
								CASE
								WHEN a.aktif >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 2
								ELSE 1
								END
							ELSE 0
							END
						) AS test_status,
						GREATEST(
							IFNULL(a.aktif, 2000-01-01),
							IFNULL(a.bitis2, 2000-01-01),
							IFNULL(a.bitis, 2000-01-01),
							IFNULL(a.assign_date, 2000-01-01)
							) AS last_event_date 
					FROM
							oturum a
					INNER JOIN kurumlar b ON a.kurum = b.id
					LEFT JOIN adaylar c ON a.userid = c.id
					WHERE
						LENGTH(a.mail) > 5
						${keyword_query}
						${date_query}
						${test_type_query}
						${company_query}
					ORDER BY
						last_event_date DESC,
						a.id DESC
						`;

			totalCounts = await request(`SELECT COUNT(*) AS total FROM (${query}) AS t`);

			final_query = query + limit_query;
			result = await request(final_query);

			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result,
				totalCounts: totalCounts[0].total,
				query: final_query
			});

			break;

		// --------------------------------------------------------------------------------------------
		case 'CAAssessments':

			date_query = (date ? `AND ( a.bitis BETWEEN '${date} 00:00:00' AND '${date} 23:59:59' )` : '');
			test_type_query = (test_type ? `AND a.sinav = ${test_type}` : '');
			assessor_query = (assessor_id ? `AND b.rater = ${assessor_id}` : '')
			company_query = (company_id ? `AND a.kurum = ${company_id}` : '')


			query = `SELECT
						a.id,
						a.token,
						CONCAT( d.ad, ' ', d.soyad ) AS candidate,
						a.sinav AS test_type,
						a.bitis AS completed_at,
						c.ad AS allocated_to,
						b.rater AS assessor_id,
						( CASE WHEN b.rater = 14 THEN 1 ELSE COALESCE(b.complete, 0) END ) AS assessment_complete
					FROM
						oturum a
						LEFT JOIN writingyorum b ON a.token = b.token
						LEFT JOIN users c ON b.rater = c.id
						INNER JOIN adaylar d ON a.userid = d.id 
					WHERE
						a.sinav >= 3 
						AND a.bitti = 1 
						AND a.demo = 0 
						${date_query}
						${test_type_query}
						${assessor_query}
						${company_query}
					HAVING
						assessment_complete = 0 
					ORDER BY
						completed_at DESC
					`;

			totalCounts = await request(`SELECT COUNT(*) AS total FROM (${query}) AS t`);

			final_query = query + limit_query;
			result = await request(final_query);

			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result,
				totalCounts: totalCounts[0].total,
				query: final_query
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CACompletedAssessments':

			query = `SELECT
						a.id,
						a.token,
						CONCAT( d.ad, ' ', d.soyad ) AS candidate,
						a.sinav AS test_type,
						a.bitis AS completed_at,
						b.date AS assessment_completed_at,
						c.ad AS allocated_to,
						b.rater AS assessor_id 
					FROM
						oturum a
						LEFT JOIN writingyorum b ON a.token = b.token
						LEFT JOIN users c ON b.rater = c.id
						INNER JOIN adaylar d ON a.userid = d.id 
					WHERE
						a.sinav >= 3 
						AND a.demo = 0 
						AND b.complete = 1 
					ORDER BY
						b.date DESC
								`;

			totalCounts = await request(`SELECT COUNT(*) AS total FROM (${query}) AS t`);

			final_query = query + limit_query;
			result = await request(final_query);

			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result,
				totalCounts: totalCounts[0].total,
				query: final_query
			});
			break;

		// --------------------------------------------------------------------------------------------

		case 'CAMobeseStaffStatistics':

			query = `SELECT
						b.id,
						b.ad AS staff_name,
						COUNT(*) AS completed_checks 
					FROM
						security_checks a
						INNER JOIN users b ON a.staff = b.id 
					GROUP BY
						b.id 
					ORDER BY
						completed_checks DESC
						`;

			result = await request(query);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAMobeseActiveStaffList':

			query = `SELECT
						a.id,
						a.ad AS staff_name 
					FROM
						users a
						INNER JOIN security_checks b ON a.id = b.staff
					WHERE 
						b.date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
					GROUP BY a.id
					ORDER BY a.ad`;

			result = await request(query);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAMobeseStaff7DayStatistics':

			query = `SELECT
						dates.date AS date,
						IFNULL( COUNT( security_checks.date ), 0 ) AS total_checks,
						users.ad AS staff_name
					FROM
						(
						SELECT
							@rownum := @rownum + 1 AS id,
							CURDATE() - INTERVAL a.a DAY AS date 
						FROM
							( SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 ) AS a,
							( SELECT @rownum := 0 ) r 
						) dates
						LEFT JOIN security_checks ON DATE( security_checks.date ) = dates.date AND security_checks.staff = ${staff_id}
						LEFT JOIN users ON security_checks.staff = users.id
					GROUP BY
						dates.id,
						dates.date`;


			result = await request(query);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});


			break;

		// --------------------------------------------------------------------------------------------

		case 'CAAssessors':

			query = `SELECT 
						t.id, 
						t.name, 
						t.avatar, 
						t.allocation, 
						t.completed_assessments, 
						t.pending_assessments 
					FROM 
						(
							SELECT 
								u.id, 
								u.ad AS NAME, 
								u.photo AS avatar, 
								u.allocation, 
								COUNT(DISTINCT CASE WHEN wy.complete = 1 AND MONTH(wy.date) = MONTH(CURRENT_DATE()) AND YEAR(wy.date) = YEAR(CURRENT_DATE()) THEN wy.id ELSE NULL END) AS completed_assessments, 
								COUNT(DISTINCT CASE WHEN wy.complete IS NULL AND MONTH(wy.date) = MONTH(CURRENT_DATE()) AND YEAR(wy.date) = YEAR(CURRENT_DATE()) THEN wy.id ELSE NULL END) AS pending_assessments 
							FROM 
								users u 
							LEFT JOIN 
								writingyorum wy ON u.id = wy.rater 
							WHERE 
								u.title LIKE '%Assessor%'
								AND u.inactive = 0
							GROUP BY 
								u.id, u.ad, u.photo, u.allocation 
						) t 
					ORDER BY 
						GREATEST(t.completed_assessments, t.pending_assessments) DESC,
						t.name ASC`;


			result = await request(query);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAAssessor7DayAssessments':

			if (!assessor_id)
				return res.json({ status: 'error', message: 'assessor_id is required' });

			query = `SELECT
						dates.date AS date,
						IFNULL( COUNT( writingyorum.date ), 0 ) AS total_assessments,
						users.ad AS assessor
					FROM
						(
						SELECT
							@rownum := @rownum + 1 AS id,
							CURDATE() - INTERVAL a.a DAY AS date 
						FROM
							( SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 ) AS a,
							( SELECT @rownum := 0 ) r 
						) dates
						LEFT JOIN writingyorum ON DATE( writingyorum.date ) = dates.date AND writingyorum.rater = ${assessor_id}
						LEFT JOIN users ON writingyorum.rater = users.id
					GROUP BY
						dates.id,
						dates.date
					`;

			result = await request(query);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAAssessorPayments':

			term_query = term ? `'${term}'` : `NOW()`;

			query = `SELECT
						u.id,
						u.ad AS name,
						u.allocation,
						u.level,
						(SELECT DATE_FORMAT(date, '%m/%Y') FROM writingyorum WHERE rater = u.id AND date IS NOT NULL ORDER BY date LIMIT 1) AS started_at,
						COUNT( wy.id ) AS assessments 
					FROM
						users u
						INNER JOIN writingyorum wy ON u.id = wy.rater
						INNER JOIN oturum o ON wy.token = o.token 
					WHERE
						u.title LIKE '%Assessor%' 
						AND wy.complete = 1 
						AND (
							wy.date BETWEEN DATE_FORMAT(DATE_SUB(${term_query}, INTERVAL 1 MONTH), '%Y-%m-25') 
							AND DATE_FORMAT(${term_query}, '%Y-%m-24')
							)
					GROUP BY
						u.id 
					ORDER BY
						assessments DESC,
						u.ad`;

			// console.log(query);

			result = await request(query);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result,
				query: (result.length > 0 ? query : '')
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAAssessorMonthlyDetails':

			query = `SELECT
						a.id,
						a.token,
						a.exam AS test_type,
						a.date AS complete_date
					FROM
						writingyorum a 
					WHERE
						a.rater = ${assessor_id}
						AND
						(
							a.date
							BETWEEN DATE_FORMAT(DATE_SUB('${term}', INTERVAL 1 MONTH ), '%Y-%m-25' ) 
							AND DATE_FORMAT('${term}','%Y-%m-24')
						)
					ORDER BY a.date`;

			// console.log(query);

			result = await request(query);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});


			break;

		//	--------------------------------------------------------------------------------------------

		case 'CAGetTestTakers':

			query = `SELECT
						a.id,
						CONCAT(a.ad, ' ', a.soyad) AS test_taker,
						b.mail AS email,
						b.token,
						COUNT(b.userid) AS total_tokens
					FROM
						adaylar a
					INNER JOIN oturum b ON a.id = b.userid
					GROUP BY
						b.userid
					ORDER BY test_taker
					`;

			totalCounts = await request(`SELECT COUNT(*) AS total FROM (${query}) AS t`);

			final_query = query + limit_query;
			result = await request(final_query);

			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result,
				totalCounts: totalCounts[0].total,
				query: final_query
			});

			break;

		//	--------------------------------------------------------------------------------------------

		case 'CA_CATestContent':

			// get test version
			query3 = `SELECT testid AS test_version FROM oturum WHERE token = ? LIMIT 1`;
			result3 = await request(query3, [token]);
			let test_version = result3[0].test_version;

			// console.log(test_type);

			query = `SELECT
						seviye AS test_level
					FROM
						sorular
					WHERE
						bolum = ?
						AND grupid = ?
					GROUP BY
						seviye
					ORDER BY
						seviye`;

			result = await request(query, [test_type, test_version]);

			// add questions to each test level

			for (let i = 0; i < result.length; i++) {

				query2 = `SELECT
							id,
							sira AS rank,
							soru AS question,
							sorutipi AS question_type,
							puan AS score,
							aplay AS audio
						FROM
							sorular
						WHERE
							grupid = ${test_version}
							AND bolum = ${test_type}
							AND seviye = ${result[i].test_level}
						ORDER BY
							sira`;

				result[i].questions = await request(query2);

			}

			res.json({
				data: result
			});

			break;

		//	--------------------------------------------------------------------------------------------

		case 'CA_CACandidateAnswers':

			query = `SELECT
						soruid AS question_id,
						basla AS start_time,
						RIGHT(tarih, 8) AS end_time,
						puan AS score 
					FROM
						yanitlar 
					WHERE
						token = '${token}'
						AND bolum = '${test_type}'
					ORDER BY
						baraj,
						soru`;

			result = await request(query);
			res.json({ status: 'success', data: result });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAGetCandidate':


			query = `SELECT
							a.id,
							a.token,
							a.sinav AS test_type,
							a.webcam AS avatar,
							a.photo AS photo,
							-- Priority logic: photo field takes precedence over webcam field
							COALESCE(a.photo, a.webcam) AS primary_avatar,
							a.mail AS email,
							b.ad AS name,
							b.soyad AS lastname,
							IFNULL(CONCAT('*******', RIGHT(b.tck, 4)), 'N/A') AS tckn,
							b.dtarih AS birthdate,
							b.tel AS phone,
							b.cinsiyet As gender,
							b.ktarih AS registration_date,
							c.kurum AS company_name
						FROM
							oturum a
						LEFT JOIN
							adaylar b ON a.userid = b.id
						LEFT JOIN
							kurumlar c ON a.kurum = c.id
						WHERE
							a.token = ?
						ORDER BY id DESC
						LIMIT 1
						`;

			result = await request(query, [token]);
			let candidate_status = (result.length > 0 ? 'success' : 'error');

			if (candidate_status === 'success') {

				const candidate_email = result[0].email;

				// Get candidate tests
				query2 = `SELECT
								a.token,
								a.testid AS test_version,
								a.sinav AS test_type,
								IFNULL(a.aktif, 0) AS active,
								CASE
									WHEN a.sinav = 1 AND a.bitti = 1 AND a.bitti2 = 1 THEN 1
									ELSE 
									(
									SELECT IF(COUNT(*) > 0, 1, 0) 
										FROM writingyorum wy 
										WHERE wy.token = a.token AND wy.complete = 1
									)
								END AS test_complete,
								a.webcam AS avatar,
								b.kurum AS company_name,
								b.logo AS company_logo,
								(
								CASE
										
										WHEN a.test_complete = 1 THEN
									CASE
											a.sinav 
											WHEN 1 THEN
										CASE
												
												WHEN ( SELECT id FROM security_checks WHERE token = a.token ) IS NULL THEN
												4 ELSE 5 
										END ELSE
										CASE
												
												WHEN ( SELECT id FROM writingyorum WHERE token = a.token AND complete = 1 ) IS NULL THEN
												3 ELSE
											CASE
													
													WHEN ( SELECT id FROM security_checks WHERE token = a.token ) IS NULL THEN
													4 ELSE 5 
												END 
												END 
												END 
													WHEN a.aktif > 0 THEN
												CASE
														
														WHEN a.aktif >= DATE_SUB( NOW(), INTERVAL 5 MINUTE ) THEN
														2 ELSE 1 
													END ELSE 0 
												END 
												) AS test_status,
												GREATEST(
													COALESCE ( a.aktif, '1900-01-01' ),
													COALESCE ( a.bitis2, '1900-01-01' ),
													COALESCE ( a.bitis, '1900-01-01' ),
													COALESCE ( a.assign_date, '1900-01-01' ) 
												) AS last_event,
												a.overall_group,
												c.score_1,
												c.score_2,
												c.total_score,
												d.cefr AS total_score_cefr,
												d.seviye AS total_score_cefr_level 
											FROM
												oturum a
												LEFT JOIN kurumlar b ON a.kurum = b.id
												LEFT JOIN (
												SELECT
													cr.token,
													cr.score_1,
													cr.score_2,
													o.sinav,
												CASE
														
														WHEN o.sinav = 1 THEN
														ROUND( SUM( cr.score_1 + cr.score_2 ) / 2 ) ELSE ROUND( cr.score_1 ) 
													END AS total_score 
												FROM
													candidate_results cr
													INNER JOIN oturum o ON cr.token = o.token 
												GROUP BY
													cr.token 
												) c ON a.token = c.token
												LEFT JOIN skala d ON ( bolum = a.sinav AND c.total_score BETWEEN d.skor1 AND d.skor2 ) 
											WHERE
												a.mail = '${candidate_email}'
											GROUP BY
												a.token
											ORDER BY
											CASE

													WHEN a.overall_group IS NULL
													OR a.overall_group = 0 THEN
														last_event ELSE a.overall_group
											END DESC`;


				const user_tests_result = await request(query2);
				const user_tests = JSON.parse(JSON.stringify(user_tests_result));

				result[0].token = token;
				result[0].tests = user_tests;


			}

			res.json({
				status: (candidate_status === 'success' ? 'success' : 'error'),
				data: result
			});


			break;

		// --------------------------------------------------------------------------------------------

		case 'CAGetCompany':

			console.log('ID: ' + company_id);

			query = `SELECT
							a.logo AS company_logo,
							a.kurum AS company_name,
							COALESCE ( SUM( CASE WHEN b.demo = 0 AND b.mail IS NOT NULL AND b.mail != '' THEN 1 ELSE 0 END ), 0 ) AS used_tokens,
							COALESCE ( SUM( CASE WHEN b.demo = 0 AND (b.mail IS NULL OR b.mail = '') THEN 1 ELSE 0 END ), 0 ) AS available_tokens,
							COALESCE ( SUM( CASE WHEN b.demo = 0 AND b.test_complete = 1 THEN 1 ELSE 0 END ), 0 ) AS completed_tests,
							(
								SELECT
									COUNT( DISTINCT adaylar.id )
								FROM
									adaylar
									INNER JOIN oturum ON adaylar.id = oturum.userid
								WHERE
									oturum.kurum = ${company_id}
									AND oturum.demo = 0
							) AS total_candidates
						FROM
							kurumlar a
							LEFT JOIN oturum b ON a.id = b.kurum
						WHERE
							a.id = ${company_id}
						GROUP BY
							a.id,
							a.kurum
							LIMIT 1`;

			result = await request(query);
			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAGetCEFR':

			const score = req.body.score;

			query = `SELECT
						cefr,
						seviye AS cefr_level
					FROM 
						skala
					WHERE
						bolum = 1
						AND skala = 2
						AND ${score} BETWEEN skor1 AND skor2
					ORDER 
						BY id DESC
					LIMIT 1`;


			result = await request(query);
			res.json({ cefr: result[0].cefr, cefr_level: result[0].cefr_level });

			break;

		// --------------------------------------------------------------------------------------------

		case 'CANextCandidate':

			query = `SELECT
						token
					 FROM
						oturum
					WHERE 
						token > ?
						AND LENGTH(mail) > 0
						AND userid > 0
						AND demo = 0
					LIMIT 1`;

			result = await request(query, [token]);
			const next_token = result.length > 0 ? result[0].token : '';

			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				token: next_token
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAGetCompanies':

			query = `SELECT
						a.id,
						a.logo,
						a.kurum AS company_name,
						( SELECT COUNT(*) FROM oturum WHERE kurum = a.id AND demo = 0 ) AS test_tokens,
						( SELECT COUNT(*) FROM sub_company_logins WHERE company_id = a.id ) AS company_users,
						( SELECT COUNT( DISTINCT userid ) FROM oturum WHERE kurum = a.id AND userid > 0 ) AS candidates 
					FROM
						kurumlar a 
					WHERE
						(
						SELECT
							COUNT(*) 
						FROM
							oturum 
						WHERE
							kurum = a.id 
							AND demo = 0 
							AND (
							IFNULL( bitis2, bitis ) > DATE_SUB( NOW(), INTERVAL 30 DAY )) 
						) > 0 
					ORDER BY
						test_tokens DESC,
						a.kurum ASC
						`;


			totalCounts = await request(`SELECT COUNT(*) AS total FROM (${query}) AS t`);

			final_query = query + limit_query;
			result = await request(final_query);

			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result,
				totalCounts: totalCounts[0].total,
				query: final_query
			});


			break;

		// --------------------------------------------------------------------------------------------

		case 'CASystemUsers':


			query = `SELECT
						id,
						ad AS name,
						title AS user_title,
						mail AS email,
						online,
						inactive 
					FROM
						users 
					WHERE
						allocation = 0 
						AND title NOT LIKE '%Assessor%'
					ORDER BY inactive, ad`;

			res.json({
				status: 'success',
				data: await request(query)
			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'CAApiLogs':

			query = `SELECT
					a.*,
					b.kurum AS client 
					FROM api_logs a
					INNER JOIN kurumlar b ON a.company_id = b.id
					ORDER BY a.create_date DESC
					`;

			totalCounts = await request(`SELECT COUNT(*) AS total FROM (${query}) AS t`);

			final_query = query + limit_query;
			result = await request(final_query);

			res.json({
				status: (result.length > 0 ? 'success' : 'error'),
				data: result,
				totalCounts: totalCounts[0].total
			});

			break;

		// --------------------------------------------------------------------------------------------
		// HTML PDF
		// --------------------------------------------------------------------------------------------

		case 'htmlToPdf':

			const pdf = await htmlToPdf();
			// res.json({ status: 'success', base64: pdf });

			res.setHeader('Content-Type', 'application/pdf');
			res.setHeader('Content-Disposition', 'inline; filename=output.pdf');
			res.send(pdf);


			break;

		// --------------------------------------------------------------------------------------------
		// Sendinblue API Transaction Email
		// --------------------------------------------------------------------------------------------

		case 'TestReminders':

			let person;

			const candidates_query = `SELECT
										a.token,
										b.ad AS name,
										b.soyad AS lastname,
										a.mail AS email,
										GREATEST(
											IFNULL( a.aktif, '2000-01-01' ),
											IFNULL( a.bitis2, '2000-01-01' ),
											IFNULL( a.bitis, '2000-01-01' ),
											IFNULL( a.assign_date, '2000-01-01' ) 
										) AS greatest_date,
										c.logo AS company_logo 
									FROM
										oturum a
										LEFT JOIN adaylar b ON a.userid = b.id
										LEFT JOIN kurumlar c ON a.kurum = c.id 
									WHERE
										a.test_complete = 0 
										AND LENGTH( a.mail ) > 5 
										AND a.demo = 0 
										AND ( a.mail NOT LIKE '%britishside%' AND a.mail NOT LIKE '%cepatest%' ) 
									GROUP BY
										a.mail 
									HAVING
										greatest_date BETWEEN DATE_SUB( NOW(), INTERVAL 24 HOUR ) 
										AND DATE_SUB( NOW(), INTERVAL 6 HOUR ) 
									ORDER BY
										greatest_date DESC`;

			const candidates = await request(candidates_query);
			const candidate_list = JSON.parse(JSON.stringify(candidates));

			candidate_list.forEach(async (candidate) => {

				person = candidate.email;
				console.log(person);

			});


			res.json({ status: 'success', data: candidates });



			// let candidateName = 'Baris Taskiran';

			// const toList = [
			// 	{
			// 		name: 'Baris Taskiran',
			// 		email: '<EMAIL>'
			// 	},
			// 	{
			// 		name: 'Baris Taskiran',
			// 		email: '<EMAIL>'
			// 	}
			// ];

			// const emailParams = {
			// 	subject: "CEPA Test Reminder",
			// 	to: toList,
			// 	template_id: 288,
			// 	params: {
			// 		'VALUE1': 'CEPA Test Hatırlatma',
			// 		'VALUE2': `${candidateName}`,
			// 		'VALUE3': `Kontrol ettiğimizde tarafınıza tanımlanan testlerden bazılarını veya hiçbirini tamamlamadığınızı gördük. Sonuçlarızın kurumunuza hızlıca iletilebilmesi testlerin tamamlanması gerekmektedir.`,
			// 		'VALUE4': 'LISTENING',
			// 		'READING_LISTENING': '123456',
			// 		'WRITING': '123456',
			// 		'VALUE5': 'Yardım Merkezi'
			// 	}
			// };


			// const result = await send_email({ ...emailParams });
			// res.json({ status: 'success', data: result });



			break;

		// --------------------------------------------------------------------------------------------

		case 'MergeVideos':

			// get the input files from bunnyCDN

			// const token = '438756';
			const inputFiles = [];

			async function mergeVideos() {
				try {
					const files = await getCandidateVideos('cepatest', token);

					files.forEach((file) => {
						inputFiles.push(file.ObjectName);
					});

					// files.slice(0, 3).forEach((file) => {
					// 	inputFiles.push(file.ObjectName);
					// });


					console.log(inputFiles);
					console.log('Merging videos...');

					const mergeVideos = await concatVideos(token, inputFiles);
					console.log('Videos merged successfully!');

					console.log(mergeVideos);

				} catch (error) {
					console.error('Error:', error);
				}
			}

			mergeVideos();

			break;

		// --------------------------------------------------------------------------------------------

		default:
			res.status(404).json({ status: 'error', message: 'Invalid API operation' });
	}

});


export default router;