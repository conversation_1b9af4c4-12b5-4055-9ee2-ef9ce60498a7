
// UI
import { Image } from 'antd';

// NPM
import { LazyLoadImage } from 'react-lazy-load-image-component';

// Utils
// (No BunnyCDN import needed; URL is hardcoded)

// Icons
import { AiOutlineUser } from 'react-icons/ai';

interface AvatarProps {
  token: string;
  avatar?: string;
  photo?: string; // New photo field from BunnyCDN - ALWAYS takes priority if not null
  preview?: boolean;
  // server?: number; // No longer used
  size?: number | null;
  rounded?: boolean;
}

const Avatar = (props: AvatarProps) => {

  // Props
  const { token, avatar, photo, preview = false, size = null, rounded = false } = props as AvatarProps;

  // Always use BunnyCDN for avatar/photo
  const avatarFile = photo ?? avatar;
  const AvatarImage = avatarFile
    ? `https://cepatest.b-cdn.net/${token}/${encodeURIComponent(avatarFile)}`
    : null;

  // Debug logging (can be removed in production)
  if (process.env.NODE_ENV === 'development' && (photo || avatar)) {
    // console.log(`[Avatar] Priority logic for token ${token}:`, {
    //   photo: photo || 'null',
    //   avatar: avatar || 'null',
    //   selectedSource: photo ? 'BunnyCDN photo (priority)' : avatar ? 'Legacy avatar' : 'none',
    //   finalUrl: AvatarImage
    // });
  }

  const NoAvatar = () => (
    <AiOutlineUser
      className={`border ${rounded ? 'rounded-circle' : ''}`}
      style={{
        width: size ? size : '100%',
        height: size ? size : 230,
        backgroundColor: '#F0F0F0',
        color: '#A0A0A0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    />
  );

  return (
    <div>
      {/* Display Logic: Show image if photo or avatar exists (always BunnyCDN) */}
      {AvatarImage ? (
        preview ? (
          <Image
            src={AvatarImage}
            className={`img-fluid candidate__Avatar ${rounded ? 'rounded-circle' : ''}`}
            alt='Candidate Avatar'
            style={size ? { width: size, height: size } : {}}
          />
        ) : (
          <LazyLoadImage
            src={AvatarImage}
            className={`img-fluid candidate__Avatar ${rounded ? 'rounded-circle' : ''}`}
            alt='Candidate Avatar'
            style={size ? { width: size, height: size } : {}}
          />
          )
      ) : (<NoAvatar />)}
    </div>
  );
}

export default Avatar;