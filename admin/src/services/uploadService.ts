import axios from 'axios';
import { message } from 'antd';
import { UPLOAD_URL } from '@/utils/consts';

interface UploadToCloudParams {
  file: Blob;
  fileName: string;
  token: string;
  uploadType?: 'avatar' | 'logo' | 'document';
}

interface UploadResponse {
  status: 'success' | 'error';
  data?: any;
  message?: string;
  url?: string;
}

/**
 * Upload file to BunnyCDN via backend API
 */
export const uploadToCloud = async ({
  file,
  fileName,
  token,
  uploadType = 'avatar'
}: UploadToCloudParams): Promise<UploadResponse> => {
  try {
    // Create FormData for file upload
    const formData = new FormData();
    formData.append('file', file);
    formData.append('zone', 'cepatest');
    formData.append('file_path', `/${token}`); // Fixed: Upload directly to token folder
    formData.append('file_name', fileName);
    formData.append('content_type', 'image/jpeg');
    formData.append('process_image', 'true');
    formData.append('max_width', '645');
    formData.append('max_height', '280');
    formData.append('quality', '90');

    // Upload to backend API
    const response = await axios.post(`${UPLOAD_URL}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 30000, // 30 second timeout
    });

    if (response.data.status === 'success') {
      return {
        status: 'success',
        data: response.data.data,
        url: `https://cepatest.b-cdn.net/${token}/${encodeURIComponent(fileName)}`,
        message: 'File uploaded successfully'
      };
    } else {
      throw new Error(response.data.message || 'Upload failed');
    }

  } catch (error: any) {
    console.error('Upload error:', error);

    let errorMessage = 'Upload failed';
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    message.error(errorMessage);

    return {
      status: 'error',
      message: errorMessage
    };
  }
};

/**
 * Generate filename with timestamp
 */
export const generateFileName = (token: string, type: string = 'Avatar'): string => {
  const now = new Date();
  const timestamp = now.toISOString()
    .replace(/[-:]/g, '')
    .replace(/\..+/, '')
    .replace('T', '_');

  return `${token}_${type}_${timestamp}.jpg`;
};

/**
 * Validate image file
 */
export const validateImageFile = (file: File, maxSizeMB: number = 5): boolean => {
  // Check file type
  if (!file.type.startsWith('image/')) {
    message.error('Please select an image file');
    return false;
  }

  // Check file size
  if (file.size > maxSizeMB * 1024 * 1024) {
    message.error(`File size must be less than ${maxSizeMB}MB`);
    return false;
  }

  return true;
};

/**
 * Convert blob to file
 */
export const blobToFile = (blob: Blob, fileName: string): File => {
  return new File([blob], fileName, { type: blob.type });
};
