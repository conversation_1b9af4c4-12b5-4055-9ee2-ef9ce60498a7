

export const ReCAPTCHA_KEY = import.meta.env.MODE === 'development' ? '' : '6Lcd7TIrAAAAANtle_PYk-91hcPiKs72x7qqEEd9';
export const TIMEZONE = 'Europe/Istanbul';
export const SYSTEM_NAME = 'CEPA Test Admin';
export const isDev = import.meta.env.MODE === 'development';

// ** Dynamic URL selection based on connection type
const isLocalhost = typeof window !== 'undefined' && window.location.hostname === 'localhost';
const useHttpsUrls = isDev && isLocalhost;

export const BACKEND_URL = useHttpsUrls
  ? import.meta.env.VITE_ADMIN2_BACKEND_URL
  : import.meta.env.VITE_IP_ADMIN2_BACKEND_URL || import.meta.env.VITE_ADMIN2_BACKEND_URL;

export const UPLOAD_URL = useHttpsUrls
  ? import.meta.env.VITE_UPLOAD_BACKEND_URL
  : import.meta.env.VITE_IP_UPLOAD_BACKEND_URL || import.meta.env.VITE_UPLOAD_BACKEND_URL;

export const HOME_PATH = '/admin'; // replaced to admin on May 4th, 2025 ~ 6.46pm EST
export const DIR = isDev ? '' : HOME_PATH;
export const CLOUD_URL = 'https://cepatest-system.b-cdn.net';
export const VIDEO_CLOUD_URL = 'https://cepatest.b-cdn.net';
export const BUNNYCDN_URL = 'https://cepatest.b-cdn.net'; // Updated to use correct CDN for avatars
export const SERVER_URL = 'https://cepacloud.britishside.org';
export const PRIMARY_COLOR = '#014483';

export const ASSESSOR_PAYMENTS_DATE = {
  month: 4,
  year: 2018
}

export const config = {
  system_name: 'CEPA Test Admin',
  system_name_short: 'CEPA',
  system_version: '2.0.0',
  system_author: 'Baris Taskiran',
  system_author_email: '<EMAIL>',
  system_copyright: 'British Side A.S.',
  system_copyright_year: '2018',
}

export const USER_ROLES = [
  { id: 1, name: 'System Admin', active: true },
  { id: 2, name: 'Test Manager', active: true },
  { id: 3, name: 'Test Assessor Staff', active: false },
  { id: 4, name: 'Academic Staff', active: false },
]


export const EMAIL_STATUS_LIST = [
  { name: 'sent', color: 'green' },
  { name: 'delivered', color: 'blue' },
  { name: 'opened', color: 'orange' },
  { name: 'click', color: 'purple' },
  { name: 'bounced', color: 'red' },
]

export const moduleList = [
  {
    value: 1,
    label: 'Administator',
    disabled: false
  },
  {
    value: 2,
    label: 'Test Issuer',
    disabled: true
  },
  {
    value: 3,
    label: 'Test Assessor',
    disabled: true
  },
  {
    value: 4,
    label: 'Academic Portal',
    disabled: true
  },
  {
    value: 5,
    label: 'Invigilator/Proctor',
    disabled: true
  }
];

export const apiEndpoints = {
  'GET': {
    'GetCandidates': "",
    'GetCandidateByEmail': "",
    'GetTestByToken': "",
    'GetTests': "",
    'GetTestLevels': "",
    'GetSingleResultByPDF': "",
    'GetOverallResultByEmail': "",
    'GetOverallResultByPDF': "",
    'SendInvitationByEmail': "",
  },
  'POST': {
    'AssignTestByEmail': ""
  }
};


export const GENDERS = [
  { label: 'Male', value: 1 },
  { label: 'Female', value: 2 },
  { label: 'Prefer not to say', value: 0 }
];


export const test_list = [
  {}, // placeholder object at index 0
  {
    id: 1,
    long: 'Reading & Listening Comprehension Assessment',
    name: 'Reading & Listening',
    product: true,
    single: 'Reading',
    code: 'RL',
    duration: '35-65',
    token_expires_hours: 2,
    color: '#0171B1',
    auto_evaluation: true,
    attribute: 'READING_LISTENING'
  },
  {
    id: 2,
    long: 'Listening Comprehension Assessment',
    name: 'Reading & Listening',
    product: false,
    single: 'Listening',
    code: 'LS',
    duration: '45-75',
    token_expires_hours: 2,
    color: '#746FB2',
    auto_evaluation: true,
    attribute: null
  },
  {
    id: 3,
    long: 'Oral Proficiency Assessment',
    name: 'Speaking',
    product: true,
    single: 'Speaking',
    code: 'SPK',
    duration: '25-35',
    token_expires_hours: 1,
    color: '#EDAA63',
    auto_evaluation: false,
    attribute: 'SPEAKING'
  },
  {
    id: 4,
    long: 'Writing Communication Assessment',
    name: 'Writing',
    product: true,
    single: 'Writing',
    code: 'WRT',
    duration: '25-35',
    token_expires_hours: 1,
    color: '#F7756C',
    auto_evaluation: false,
    attribute: 'WRITING'
  }
];

export const langs = [
  { id: 1, name: 'English', code: 'en' },
  { id: 2, name: 'Türkçe', code: 'tr' },
];


export const test_category_list = [
  {
    id: 0,
    name: 'General Proficiency',
  },
  {
    id: 1,
    name: 'General Proficiency',
  },
  {
    id: 2,
    name: 'Pre-Employment',
  },
];

export const test_cefr_levels = [
  {
    id: 1,
    name: 'pre-A1',
    global_name: 'A1',
  },
  {
    id: 2,
    name: 'A1',
    global_name: 'A2',
  },
  {
    id: 3,
    name: 'A2',
    global_name: 'B1',
  },
  {
    id: 4,
    name: 'B1',
    global_name: 'B2',
  },
  {
    id: 5,
    name: 'B2',
    global_name: 'C1',
  },
  {
    id: 6,
    name: 'C1/C2',
    global_name: 'C2',
  },

];


export const question_types = [
  {},
  {
    id: 1,
    name: 'Single Choice',
  },
  {
    id: 2,
    name: 'Multiple Choice',
  },
  {
    id: 3,
    name: 'Reordering',
  },
  {
    id: 4,
    name: 'Drag & Drop',
  }
];


export const SUSPECTION_REASONS = [

  // Use of Phone
  {
    value: 0,
    label: 'Other',
    descriptions: {
      tr: 'Lütfen şüphelenme nedenini belirtiniz.',
      en: 'Please specify the reason for suspicion.'
    }
  },

  // Use of Phone
  {
    value: 1,
    label: 'Phone Usage',
    descriptions: {
      tr: 'Sınav esnasında adayın telefon kullandığı tespit edilmiştir. Bu durum, adayın sınav sürecinin şüpheli olarak değerlendirilmesine sebep olmuştur.',
      en: 'The candidate was found using a phone during the exam. '
    }
  },

  // Reading Answers from Another Device
  {
    value: 2,
    label: 'Reading from Another Device',
    descriptions: {
      tr: 'Adayın, sınav sorularının yanıtlarını farklı bir cihazdan okuyarak cevapladığı tespit edilmiştir. Bu durum, adayın sınav sürecinin şüpheli olarak değerlendirilmesine sebep olmuştur.',
      en: 'The candidate was found reading answers from another device during the exam. '
    }
  },

  // Receiving Assistance
  {
    value: 3,
    label: 'Receiving Assistance',
    descriptions: {
      tr: 'Sınav esnasında adayın yanında bir kişinin bulunarak adaya yardım ettiği tespit edilmiştir. Bu durum, adayın sınav sürecinin şüpheli olarak değerlendirilmesine sebep olmuştur.',
      en: 'It was found that someone was assisting the candidate during the exam. '
    }
  },

  // No Camera Footage
  {
    value: 4,
    label: 'No Camera Footage',
    descriptions: {
      tr: 'Adayın sınav esnasında kamera görüntüsünün bulunmadığı tespit edilmiştir. Bu durum, adayın sınav sürecinin şüpheli olarak değerlendirilmesine sebep olmuştur.',
      en: 'The candidate had no camera footage during the exam. '
    }
  },

  // Camera Covered
  {
    value: 5,
    label: 'Camera Covered',
    descriptions: {
      tr: 'Sınav sırasında adayın kamera kapağının kapalı olduğu tespit edilmiştir. Bu durum, adayın sınav sürecinin şüpheli olarak değerlendirilmesine sebep olmuştur.',
      en: 'The candidate’s camera cover was found to be closed during the exam. '
    }
  },

  // Third-Party Software
  {
    value: 6,
    label: 'Third-Party Software',
    descriptions: {
      tr: 'Sınav esnasında adayın kamera görüntüsünün, kullanılan üçüncü parti yazılım nedeniyle engellendiği tespit edilmiştir. Bu durum, adayın sınav sürecinin şüpheli olarak değerlendirilmesine sebep olmuştur.',
      en: 'The candidate’s camera footage was blocked due to third-party software during the exam. '
    }
  },
]

// ** --------------------------------------------------------------------------------------
// ** CITY & DISTRICT LIST 
// ** --------------------------------------------------------------------------------------

export const city_list = [{
  city: "Adana",
  id: 1,
  districts: ["Aladağ", "Ceyhan", "Çukurova", "Feke", "İmamoğlu", "Karaisalı", "Karataş", "Kozan", "Pozantı", "Saimbeyli", "Sarıçam", "Seyhan", "Tufanbeyli", "Yumurtalık", "Yüreğir"]
}, {
  city: "Adıyaman",
  id: 2,
  districts: ["Besni", "Çelikhan", "Gerger", "Gölbaşı", "Kahta", "Merkez", "Samsat", "Sincik", "Tut"]
}, {
  city: "Afyonkarahisar",
  id: 3,
  districts: ["Başmakçı", "Bayat", "Bolvadin", "Çay", "Çobanlar", "Dazkırı", "Dinar", "Emirdağ", "Evciler", "Hocalar", "İhsaniye", "İscehisar", "Kızılören", "Merkez", "Sandıklı", "Sinanpaşa", "Sultandağı", "Şuhut"]
}, {
  city: "Ağrı",
  id: 4,
  districts: ["Diyadin", "Doğubayazıt", "Eleşkirt", "Hamur", "Merkez", "Patnos", "Taşlıçay", "Tutak"]
}, {
  city: "Amasya",
  id: 5,
  districts: ["Göynücek", "Gümüşhacıköy", "Hamamözü", "Merkez", "Merzifon", "Suluova", "Taşova"]
}, {
  city: "Ankara",
  id: 6,
  districts: ["Altındağ", "Ayaş", "Bala", "Beypazarı", "Çamlıdere", "Çankaya", "Çubuk", "Elmadağ", "Güdül", "Haymana", "Kalecik", "Kızılcahamam", "Nallıhan", "Polatlı", "Şereflikoçhisar", "Yenimahalle", "Gölbaşı", "Keçiören", "Mamak", "Sincan", "Kazan", "Akyurt", "Etimesgut", "Evren", "Pursaklar"]
}, {
  city: "Antalya",
  id: 7,
  districts: ["Akseki", "Alanya", "Elmalı", "Finike", "Gazipaşa", "Gündoğmuş", "Kaş", "Korkuteli", "Kumluca", "Manavgat", "Serik", "Demre", "İbradı", "Kemer", "Aksu", "Döşemealtı", "Kepez", "Konyaaltı", "Muratpaşa"]
}, {
  city: "Artvin",
  id: 8,
  districts: ["Ardanuç", "Arhavi", "Merkez", "Borçka", "Hopa", "Şavşat", "Yusufeli", "Murgul"]
}, {
  city: "Aydın",
  id: 9,
  districts: ["Merkez", "Bozdoğan", "Efeler", "Çine", "Germencik", "Karacasu", "Koçarlı", "Kuşadası", "Kuyucak", "Nazilli", "Söke", "Sultanhisar", "Yenipazar", "Buharkent", "İncirliova", "Karpuzlu", "Köşk", "Didim"]
}, {
  city: "Balıkesir",
  id: 10,
  districts: ["Altıeylül", "Ayvalık", "Merkez", "Balya", "Bandırma", "Bigadiç", "Burhaniye", "Dursunbey", "Edremit", "Erdek", "Gönen", "Havran", "İvrindi", "Karesi", "Kepsut", "Manyas", "Savaştepe", "Sındırgı", "Gömeç", "Susurluk", "Marmara"]
}, {
  city: "Bilecik",
  id: 11,
  districts: ["Merkez", "Bozüyük", "Gölpazarı", "Osmaneli", "Pazaryeri", "Söğüt", "Yenipazar", "İnhisar"]
}, {
  city: "Bingöl",
  id: 12,
  districts: ["Merkez", "Genç", "Karlıova", "Kiğı", "Solhan", "Adaklı", "Yayladere", "Yedisu"]
}, {
  city: "Bitlis",
  id: 13,
  districts: ["Adilcevaz", "Ahlat", "Merkez", "Hizan", "Mutki", "Tatvan", "Güroymak"]
}, {
  city: "Bolu",
  id: 14,
  districts: ["Merkez", "Gerede", "Göynük", "Kıbrıscık", "Mengen", "Mudurnu", "Seben", "Dörtdivan", "Yeniçağa"]
}, {
  city: "Burdur",
  id: 15,
  districts: ["Ağlasun", "Bucak", "Merkez", "Gölhisar", "Tefenni", "Yeşilova", "Karamanlı", "Kemer", "Altınyayla", "Çavdır", "Çeltikçi"]
}, {
  city: "Bursa",
  id: 16,
  districts: ["Gemlik", "İnegöl", "İznik", "Karacabey", "Keles", "Mudanya", "Mustafakemalpaşa", "Orhaneli", "Orhangazi", "Yenişehir", "Büyükorhan", "Harmancık", "Nilüfer", "Osmangazi", "Yıldırım", "Gürsu", "Kestel"]
}, {
  city: "Çanakkale",
  id: 17,
  districts: ["Ayvacık", "Bayramiç", "Biga", "Bozcaada", "Çan", "Merkez", "Eceabat", "Ezine", "Gelibolu", "Gökçeada", "Lapseki", "Yenice"]
}, {
  city: "Çankırı",
  id: 18,
  districts: ["Merkez", "Çerkeş", "Eldivan", "Ilgaz", "Kurşunlu", "Orta", "Şabanözü", "Yapraklı", "Atkaracalar", "Kızılırmak", "Bayramören", "Korgun"]
}, {
  city: "Çorum",
  id: 19,
  districts: ["Alaca", "Bayat", "Merkez", "İskilip", "Kargı", "Mecitözü", "Ortaköy", "Osmancık", "Sungurlu", "Boğazkale", "Uğurludağ", "Dodurga", "Laçin", "Oğuzlar"]
}, {
  city: "Denizli",
  id: 20,
  districts: ["Acıpayam", "Buldan", "Çal", "Çameli", "Çardak", "Çivril", "Merkez", "Merkezefendi", "Pamukkale", "Güney", "Kale", "Sarayköy", "Tavas", "Babadağ", "Bekilli", "Honaz", "Serinhisar", "Baklan", "Beyağaç", "Bozkurt"]
}, {
  city: "Diyarbakır",
  id: 21,
  districts: ["Kocaköy", "Çermik", "Çınar", "Çüngüş", "Dicle", "Ergani", "Hani", "Hazro", "Kulp", "Lice", "Silvan", "Eğil", "Bağlar", "Kayapınar", "Sur", "Yenişehir", "Bismil"]
}, {
  city: "Edirne",
  id: 22,
  districts: ["Merkez", "Enez", "Havsa", "İpsala", "Keşan", "Lalapaşa", "Meriç", "Uzunköprü", "Süloğlu"]
}, {
  city: "Elazığ",
  id: 23,
  districts: ["Ağın", "Baskil", "Merkez", "Karakoçan", "Keban", "Maden", "Palu", "Sivrice", "Arıcak", "Kovancılar", "Alacakaya"]
}, {
  city: "Erzincan",
  id: 24,
  districts: ["Çayırlı", "Merkez", "İliç", "Kemah", "Kemaliye", "Refahiye", "Tercan", "Üzümlü", "Otlukbeli"]
}, {
  city: "Erzurum",
  id: 25,
  districts: ["Aşkale", "Çat", "Hınıs", "Horasan", "İspir", "Karayazı", "Narman", "Oltu", "Olur", "Pasinler", "Şenkaya", "Tekman", "Tortum", "Karaçoban", "Uzundere", "Pazaryolu", "Köprüköy", "Palandöken", "Yakutiye", "Aziziye"]
}, {
  city: "Eskişehir",
  id: 26,
  districts: ["Çifteler", "Mahmudiye", "Mihalıççık", "Sarıcakaya", "Seyitgazi", "Sivrihisar", "Alpu", "Beylikova", "İnönü", "Günyüzü", "Han", "Mihalgazi", "Odunpazarı", "Tepebaşı"]
}, {
  city: "Gaziantep",
  id: 27,
  districts: ["Araban", "İslahiye", "Nizip", "Oğuzeli", "Yavuzeli", "Şahinbey", "Şehitkamil", "Karkamış", "Nurdağı"]
}, {
  city: "Giresun",
  id: 28,
  districts: ["Alucra", "Bulancak", "Dereli", "Espiye", "Eynesil", "Merkez", "Görele", "Keşap", "Şebinkarahisar", "Tirebolu", "Piraziz", "Yağlıdere", "Çamoluk", "Çanakçı", "Doğankent", "Güce"]
}, {
  city: "Gümüşhane",
  id: 29,
  districts: ["Merkez", "Kelkit", "Şiran", "Torul", "Köse", "Kürtün"]
}, {
  city: "Hakkari",
  id: 30,
  districts: ["Çukurca", "Merkez", "Şemdinli", "Yüksekova"]
}, {
  city: "Hatay",
  id: 31,
  districts: ["Altınözü", "Arsuz", "Defne", "Dörtyol", "Hassa", "Antakya", "İskenderun", "Kırıkhan", "Payas", "Reyhanlı", "Samandağ", "Yayladağı", "Erzin", "Belen", "Kumlu"]
}, {
  city: "Isparta",
  id: 32,
  districts: ["Atabey", "Eğirdir", "Gelendost", "Merkez", "Keçiborlu", "Senirkent", "Sütçüler", "Şarkikaraağaç", "Uluborlu", "Yalvaç", "Aksu", "Gönen", "Yenişarbademli"]
}, {
  city: "Mersin",
  id: 33,
  districts: ["Anamur", "Erdemli", "Gülnar", "Mut", "Silifke", "Tarsus", "Aydıncık", "Bozyazı", "Çamlıyayla", "Akdeniz", "Mezitli", "Toroslar", "Yenişehir"]
}, {
  city: "İstanbul",
  id: 34,
  districts: ["Adalar", "Bakırköy", "Beşiktaş", "Beykoz", "Beyoğlu", "Çatalca", "Eyüp", "Fatih", "Gaziosmanpaşa", "Kadıköy", "Kartal", "Sarıyer", "Silivri", "Şile", "Şişli", "Üsküdar", "Zeytinburnu", "Büyükçekmece", "Kağıthane", "Küçükçekmece", "Pendik", "Ümraniye", "Bayrampaşa", "Avcılar", "Bağcılar", "Bahçelievler", "Güngören", "Maltepe", "Sultanbeyli", "Tuzla", "Esenler", "Arnavutköy", "Ataşehir", "Başakşehir", "Beylikdüzü", "Çekmeköy", "Esenyurt", "Sancaktepe", "Sultangazi"]
}, {
  city: "İzmir",
  id: 35,
  districts: ["Aliağa", "Bayındır", "Bergama", "Bornova", "Çeşme", "Dikili", "Foça", "Karaburun", "Karşıyaka", "Kemalpaşa", "Kınık", "Kiraz", "Menemen", "Ödemiş", "Seferihisar", "Selçuk", "Tire", "Torbalı", "Urla", "Beydağ", "Buca", "Konak", "Menderes", "Balçova", "Çiğli", "Gaziemir", "Narlıdere", "Güzelbahçe", "Bayraklı", "Karabağlar"]
}, {
  city: "Kars",
  id: 36,
  districts: ["Arpaçay", "Digor", "Kağızman", "Merkez", "Sarıkamış", "Selim", "Susuz", "Akyaka"]
}, {
  city: "Kastamonu",
  id: 37,
  districts: ["Abana", "Araç", "Azdavay", "Bozkurt", "Cide", "Çatalzeytin", "Daday", "Devrekani", "İnebolu", "Merkez", "Küre", "Taşköprü", "Tosya", "İhsangazi", "Pınarbaşı", "Şenpazar", "Ağlı", "Doğanyurt", "Hanönü", "Seydiler"]
}, {
  city: "Kayseri",
  id: 38,
  districts: ["Bünyan", "Develi", "Felahiye", "İncesu", "Pınarbaşı", "Sarıoğlan", "Sarız", "Tomarza", "Yahyalı", "Yeşilhisar", "Akkışla", "Talas", "Kocasinan", "Melikgazi", "Hacılar", "Özvatan"]
}, {
  city: "Kırklareli",
  id: 39,
  districts: ["Babaeski", "Demirköy", "Merkez", "Kofçaz", "Lüleburgaz", "Pehlivanköy", "Pınarhisar", "Vize"]
}, {
  city: "Kırşehir",
  id: 40,
  districts: ["Çiçekdağı", "Kaman", "Merkez", "Mucur", "Akpınar", "Akçakent", "Boztepe"]
}, {
  city: "Kocaeli",
  id: 41,
  districts: ["Gebze", "Gölcük", "Kandıra", "Karamürsel", "Körfez", "Derince", "Başiskele", "Çayırova", "Darıca", "Dilovası", "İzmit", "Kartepe"]
}, {
  city: "Konya",
  id: 42,
  districts: ["Akşehir", "Beyşehir", "Bozkır", "Cihanbeyli", "Çumra", "Doğanhisar", "Ereğli", "Hadim", "Ilgın", "Kadınhanı", "Karapınar", "Kulu", "Sarayönü", "Seydişehir", "Yunak", "Akören", "Altınekin", "Derebucak", "Hüyük", "Karatay", "Meram", "Selçuklu", "Taşkent", "Ahırlı", "Çeltik", "Derbent", "Emirgazi", "Güneysınır", "Halkapınar", "Tuzlukçu", "Yalıhüyük"]
}, {
  city: "Kütahya",
  id: 43,
  districts: ["Altıntaş", "Domaniç", "Emet", "Gediz", "Merkez", "Simav", "Tavşanlı", "Aslanapa", "Dumlupınar", "Hisarcık", "Şaphane", "Çavdarhisar", "Pazarlar"]
}, {
  city: "Malatya",
  id: 44,
  districts: ["Akçadağ", "Arapgir", "Arguvan", "Darende", "Doğanşehir", "Hekimhan", "Merkez", "Pütürge", "Yeşilyurt", "Battalgazi", "Doğanyol", "Kale", "Kuluncak", "Yazıhan"]
}, {
  city: "Manisa",
  id: 45,
  districts: ["Akhisar", "Alaşehir", "Demirci", "Gördes", "Kırkağaç", "Kula", "Merkez", "Salihli", "Sarıgöl", "Saruhanlı", "Selendi", "Soma", "Şehzadeler", "Yunusemre", "Turgutlu", "Ahmetli", "Gölmarmara", "Köprübaşı"]
}, {
  city: "Kahramanmaraş",
  id: 46,
  districts: ["Afşin", "Andırın", "Dulkadiroğlu", "Onikişubat", "Elbistan", "Göksun", "Merkez", "Pazarcık", "Türkoğlu", "Çağlayancerit", "Ekinözü", "Nurhak"]
}, {
  city: "Mardin",
  id: 47,
  districts: ["Derik", "Kızıltepe", "Artuklu", "Merkez", "Mazıdağı", "Midyat", "Nusaybin", "Ömerli", "Savur", "Dargeçit", "Yeşilli"]
}, {
  city: "Muğla",
  id: 48,
  districts: ["Bodrum", "Datça", "Fethiye", "Köyceğiz", "Marmaris", "Menteşe", "Milas", "Ula", "Yatağan", "Dalaman", "Seydikemer", "Ortaca", "Kavaklıdere"]
}, {
  city: "Muş",
  id: 49,
  districts: ["Bulanık", "Malazgirt", "Merkez", "Varto", "Hasköy", "Korkut"]
}, {
  city: "Nevşehir",
  id: 50,
  districts: ["Avanos", "Derinkuyu", "Gülşehir", "Hacıbektaş", "Kozaklı", "Merkez", "Ürgüp", "Acıgöl"]
}, {
  city: "Niğde",
  id: 51,
  districts: ["Bor", "Çamardı", "Merkez", "Ulukışla", "Altunhisar", "Çiftlik"]
}, {
  city: "Ordu",
  id: 52,
  districts: ["Akkuş", "Altınordu", "Aybastı", "Fatsa", "Gölköy", "Korgan", "Kumru", "Mesudiye", "Perşembe", "Ulubey", "Ünye", "Gülyalı", "Gürgentepe", "Çamaş", "Çatalpınar", "Çaybaşı", "İkizce", "Kabadüz", "Kabataş"]
}, {
  city: "Rize",
  id: 53,
  districts: ["Ardeşen", "Çamlıhemşin", "Çayeli", "Fındıklı", "İkizdere", "Kalkandere", "Pazar", "Merkez", "Güneysu", "Derepazarı", "Hemşin", "İyidere"]
}, {
  city: "Sakarya",
  id: 54,
  districts: ["Akyazı", "Geyve", "Hendek", "Karasu", "Kaynarca", "Sapanca", "Kocaali", "Pamukova", "Taraklı", "Ferizli", "Karapürçek", "Söğütlü", "Adapazarı", "Arifiye", "Erenler", "Serdivan"]
}, {
  city: "Samsun",
  id: 55,
  districts: ["Alaçam", "Bafra", "Çarşamba", "Havza", "Kavak", "Ladik", "Terme", "Vezirköprü", "Asarcık", "Ondokuzmayıs", "Salıpazarı", "Tekkeköy", "Ayvacık", "Yakakent", "Atakum", "Canik", "İlkadım"]
}, {
  city: "Siirt",
  id: 56,
  districts: ["Baykan", "Eruh", "Kurtalan", "Pervari", "Merkez", "Şirvan", "Tillo"]
}, {
  city: "Sinop",
  id: 57,
  districts: ["Ayancık", "Boyabat", "Durağan", "Erfelek", "Gerze", "Merkez", "Türkeli", "Dikmen", "Saraydüzü"]
}, {
  city: "Sivas",
  id: 58,
  districts: ["Divriği", "Gemerek", "Gürün", "Hafik", "İmranlı", "Kangal", "Koyulhisar", "Merkez", "Suşehri", "Şarkışla", "Yıldızeli", "Zara", "Akıncılar", "Altınyayla", "Doğanşar", "Gölova", "Ulaş"]
}, {
  city: "Tekirdağ",
  id: 59,
  districts: ["Çerkezköy", "Çorlu", "Ergene", "Hayrabolu", "Malkara", "Muratlı", "Saray", "Süleymanpaşa", "Kapaklı", "Şarköy", "Marmaraereğlisi"]
}, {
  city: "Tokat",
  id: 60,
  districts: ["Almus", "Artova", "Erbaa", "Niksar", "Reşadiye", "Merkez", "Turhal", "Zile", "Pazar", "Yeşilyurt", "Başçiftlik", "Sulusaray"]
}, {
  city: "Trabzon",
  id: 61,
  districts: ["Akçaabat", "Araklı", "Arsin", "Çaykara", "Maçka", "Of", "Ortahisar", "Sürmene", "Tonya", "Vakfıkebir", "Yomra", "Beşikdüzü", "Şalpazarı", "Çarşıbaşı", "Dernekpazarı", "Düzköy", "Hayrat", "Köprübaşı"]
}, {
  city: "Tunceli",
  id: 62,
  districts: ["Çemişgezek", "Hozat", "Mazgirt", "Nazımiye", "Ovacık", "Pertek", "Pülümür", "Merkez"]
}, {
  city: "Şanlıurfa",
  id: 63,
  districts: ["Akçakale", "Birecik", "Bozova", "Ceylanpınar", "Eyyübiye", "Halfeti", "Haliliye", "Hilvan", "Karaköprü", "Siverek", "Suruç", "Viranşehir", "Harran"]
}, {
  city: "Uşak",
  id: 64,
  districts: ["Banaz", "Eşme", "Karahallı", "Sivaslı", "Ulubey", "Merkez"]
}, {
  city: "Van",
  id: 65,
  districts: ["Başkale", "Çatak", "Erciş", "Gevaş", "Gürpınar", "İpekyolu", "Muradiye", "Özalp", "Tuşba", "Bahçesaray", "Çaldıran", "Edremit", "Saray"]
}, {
  city: "Yozgat",
  id: 66,
  districts: ["Akdağmadeni", "Boğazlıyan", "Çayıralan", "Çekerek", "Sarıkaya", "Sorgun", "Şefaatli", "Yerköy", "Merkez", "Aydıncık", "Çandır", "Kadışehri", "Saraykent", "Yenifakılı"]
}, {
  city: "Zonguldak",
  id: 67,
  districts: ["Çaycuma", "Devrek", "Ereğli", "Merkez", "Alaplı", "Gökçebey"]
}, {
  city: "Aksaray",
  id: 68,
  districts: ["Ağaçören", "Eskil", "Gülağaç", "Güzelyurt", "Merkez", "Ortaköy", "Sarıyahşi"]
}, {
  city: "Bayburt",
  id: 69,
  districts: ["Merkez", "Aydıntepe", "Demirözü"]
}, {
  city: "Karaman",
  id: 70,
  districts: ["Ermenek", "Merkez", "Ayrancı", "Kazımkarabekir", "Başyayla", "Sarıveliler"]
}, {
  city: "Kırıkkale",
  id: 71,
  districts: ["Delice", "Keskin", "Merkez", "Sulakyurt", "Bahşili", "Balışeyh", "Çelebi", "Karakeçili", "Yahşihan"]
}, {
  city: "Batman",
  id: 72,
  districts: ["Merkez", "Beşiri", "Gercüş", "Kozluk", "Sason", "Hasankeyf"]
}, {
  city: "Şırnak",
  id: 73,
  districts: ["Beytüşşebap", "Cizre", "İdil", "Silopi", "Merkez", "Uludere", "Güçlükonak"]
}, {
  city: "Bartın",
  id: 74,
  districts: ["Merkez", "Kurucaşile", "Ulus", "Amasra"]
}, {
  city: "Ardahan",
  id: 75,
  districts: ["Merkez", "Çıldır", "Göle", "Hanak", "Posof", "Damal"]
}, {
  city: "Iğdır",
  id: 76,
  districts: ["Aralık", "Merkez", "Tuzluca", "Karakoyunlu"]
}, {
  city: "Yalova",
  id: 77,
  districts: ["Merkez", "Altınova", "Armutlu", "Çınarcık", "Çiftlikköy", "Termal"]
}, {
  city: "Karabük",
  id: 78,
  districts: ["Eflani", "Eskipazar", "Merkez", "Ovacık", "Safranbolu", "Yenice"]
}, {
  city: "Kilis",
  id: 79,
  districts: ["Merkez", "Elbeyli", "Musabeyli", "Polateli"]
}, {
  city: "Osmaniye",
  id: 80,
  districts: ["Bahçe", "Kadirli", "Merkez", "Düziçi", "Hasanbeyli", "Sumbas", "Toprakkale"]
}, {
  city: "Düzce",
  id: 81,
  districts: ["Akçakoca", "Merkez", "Yığılca", "Cumayeri", "Gölyaka", "Çilimli", "Gümüşova", "Kaynaşlı"]
}];



