import React, { useState } from 'react';
import { Button, message, Space, Typography, Alert } from 'antd';
import { UploadOutlined, UserOutlined } from '@ant-design/icons';
import axios from 'axios';

// Components
import ImageCropper from '@/components/ImageCropper';

// Services
// Using direct backend endpoint instead of uploadService

// Store
import { useModalStore } from '@/store/useModalStore';
import { useCandidateStore } from '@/store/useCandidateStore';

// Hooks
import useCandidate from '@/hooks/useCandidate';

// Utils
import { BACKEND_URL } from '@/utils/consts';

const { Title, Text } = Typography;

interface UploadAvatarProps {
  token: string;
  onUploadSuccess?: () => void;
}

const UploadAvatar: React.FC<UploadAvatarProps> = ({ token, onUploadSuccess }) => {
  const [cropperVisible, setCropperVisible] = useState(false);
  const [uploading, setUploading] = useState(false);

  // Store
  const setCandidate = useCandidateStore(state => state.setCandidate);
  const candidate = useCandidateStore(state => state.candidate);

  // Define aspect ratio options
  const aspectRatioOptions = [
    {
      label: 'Landscape (8:5)',
      value: 1.6,
      description: 'Wide format, good for professional photos'
    },
    {
      label: 'Square (1:1)',
      value: 1.0,
      description: 'Square format, good for profile pictures'
    }
  ];

  // Store
  const { hideModal } = useModalStore();

  // Hooks
  const { fetchCandidate } = useCandidate();

  // Handle cropped image upload
  const handleCropComplete = async (croppedImageBlob: Blob, originalFileName: string) => {
    if (!token) {
      message.error('Token is required');
      return;
    }

    setUploading(true);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', croppedImageBlob, 'avatar.jpg');
      formData.append('token', token);

      // Upload to backend upload-avatar endpoint
      const uploadResult = await axios.post(`${BACKEND_URL}/upload-avatar`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000, // 30 second timeout
      });

      if (uploadResult.data.status === 'success') {
        message.success('Avatar uploaded successfully!');

        // Extract filename from response
        const fileName = uploadResult.data.data.fileName;

        // Update candidate store with new photo
        if (candidate && fileName) {
          setCandidate({
            ...candidate,
            photo: fileName
          });
        }

        // Refresh candidate data
        if (fetchCandidate) {
          await fetchCandidate(token);
        }

        // Call success callback
        if (onUploadSuccess) {
          onUploadSuccess();
        }

        // Close modal
        hideModal();

      } else {
        message.error('Upload failed: ' + uploadResult.data.message);
      }

    } catch (error) {
      console.error('Upload error:', error);
      message.error('Failed to upload avatar');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div style={{ padding: '20px 0' }}>
      <div style={{ textAlign: 'center', marginBottom: 24 }}>
        <UserOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
        <Title level={4}>Upload Candidate Avatar</Title>
        <Text type="secondary">
          Upload and crop a photo for candidate: <strong>{token}</strong>
        </Text>
      </div>

      <Alert
        message="Image Requirements"
        description={
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            <li>Choose between Landscape (8:5) or Square (1:1) format</li>
            <li>Minimum width: 280px</li>
            <li>Maximum file size: 5MB</li>
            <li>Supported formats: JPG, PNG, GIF, WebP</li>
          </ul>
        }
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <div style={{ textAlign: 'center' }}>
        <Space direction="vertical" size="large">
          <Button
            type="primary"
            size="large"
            icon={<UploadOutlined />}
            onClick={() => setCropperVisible(true)}
            loading={uploading}
            style={{ minWidth: 200 }}
          >
            {uploading ? 'Uploading...' : 'Select Image'}
          </Button>

          <Text type="secondary">
            Click to select an image file and crop it for the candidate avatar
          </Text>
        </Space>
      </div>

      <ImageCropper
        visible={cropperVisible}
        onCancel={() => setCropperVisible(false)}
        onCropComplete={handleCropComplete}
        aspectRatio={1.6} // Default to landscape ratio
        aspectRatioOptions={aspectRatioOptions}
        minWidth={280}
        maxFileSize={5}
        title="Upload Candidate Avatar"
        cropTitle="Crop Candidate Avatar"
      />
    </div>
  );
};

export default UploadAvatar;
