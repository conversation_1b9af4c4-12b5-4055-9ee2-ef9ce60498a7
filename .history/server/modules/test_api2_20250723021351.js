import express from 'express';
const router = express.Router();

import jwt from 'jsonwebtoken';
import { checkDatabaseConnection, post } from './mysql/mysql.js';

// Config
import { jwtSecret } from './config.js';

// Functions
import { sendEmail } from './functions/SendEmail.js';

// OPS
import { UpdateDeviceLogs } from './ops/DeviceLogger.js';
import { uploadToBunnyCDN } from './ops/Upload.js';
import { AllocateAssessor } from './ops/AllocateToAssessor.js';
import { GetCandidateData } from './ops/CandidateData.js';
import { GetQuestionData } from './ops/GetQuestionData.js';


// NPM Modules
import moment from 'moment-timezone';

// import soap from 'soap';
// import bodyParser from 'body-parser';
// import fetch from 'node-fetch';

import dotenv from 'dotenv';
dotenv.config();

import multer from 'multer';
const upload = multer();

import { EventEmitter } from 'events';
const Bus = new EventEmitter();
Bus.setMaxListeners(500);


router.use(express.json());
router.use(express.urlencoded({ extended: true, limit: '5mb' }));

router.use(express.raw({
	type: 'application/octet-stream',
	limit: '5mb',
}));

const isDev = process.env.NODE_ENV === 'development';
const TIMEZONE = 'Europe/Istanbul';
const now = moment.tz(TIMEZONE).format('YYYY-MM-DD HH:mm:ss');

// ====================================================================================================

router.use('/:op', async (req, res) => {

	// Check the database connection (disabled for demo mode):
	// await checkDatabaseConnection();

	const op = req.params.op;

	const {
		dev_mode,
		jwt_token,
		db_event,
		type,
		rs,
		token,
		email,
		lang,
		test_type,
		test_module,
		test_version,
		company_id,
		test_taker,
		name,
		lastname,
		tckn,
		phone,
		occupation,
		department,
		education,
		birthdate,
		gender,
		mother_language,
		nationality,
		area_code,
		tckn_verification,
		job_position,
		test_take_reason,
		seconds,
		file_name,
		answer,
		question_id,
		question_rank,
		blocked_time,
		flag,
		query_text
	} = req.body;

	function r(data) {
		res.status(200).json({ ...data });
	}

	// ====================================================================================================

	let api_result = {};

	let sql, sql2, sql3, sql4, sql5;
	let query, query2, query3, query4, query5;
	let row, row2, row3, row4, row5;



	switch (op) {

		// --------------------------------------------------------------------------------------------

		case 'CDNUpload':

			upload.single('buffer')(req, res, async (err) => {

				if (err) {
					r({ status: 'error', message: err.message });
					return;
				}

				if (!req.file) {
					r({ status: 'error', message: 'File is missing' });
					return;
				}

				const { file_name, path, type: upload_type, token, start_date } = req.body;
				const buffer = req.file.buffer;

				if (!file_name || !buffer || !path) {
					r({ status: 'error', message: 'Missing parameters: file_name, buffer or path is missing' });
					return;
				}

				r(await uploadToBunnyCDN({ fileName: file_name, path, buffer, token, upload_type, start_date }));

			});

			break;

		// --------------------------------------------------------------------------------------------

		case 'SendEmail':

			if (!rs) {
				r({ status: 'error', message: 'Missing parameters' });
				return;
			}
			r(await sendEmail(rs));
			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateTimer':

			if (!token || !seconds || !test_type) { errorHandler(); return; }
			const flag_count = flag ?? null;

			try {

				const column = (test_type == 2 ? 'sure2' : 'sure');
				query = await post(`UPDATE oturum SET ${column} = ?, aktif = ?, flag = ?, blocked_time = ? WHERE token = ?`,
					[
						seconds,
						now,
						flag_count,
						blocked_time,
						token
					]);
				if (query.affectedRows > 0) r({ status: 'success' });

			} catch (err) {
				r({ status: 'error', message: err.message });

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'ValidateToken':

			if (!jwt_token) {
				r({ status: 'error', message: 'Token is missing' });
				return;
			}

			try {
				const decoded = jwt.verify(jwt_token, jwtSecret);
				r({
					status: 'success',
					data: decoded,
					message: 'JWT Token is valid',
				});
			} catch (err) {
				console.error('JWT Verification Error:', err.message);
				r({ status: 'error', message: err.message });
			}
			break;

		// --------------------------------------------------------------------------------------------

		case 'GetCandidateData':

			if (!token || !email) {
				r({ status: 'error', message: 'Token is missing' });
				return;
			}

			r(await GetCandidateData(email, token, req));
			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateCandidateLanguage':

			if (!token || !lang) {
				r({ status: 'error', message: 'Missing parameters: token or lang is missing' });
				return;
			}

			try {
				query = await post(`UPDATE oturum SET lang = ? WHERE token = ?`, [lang, token]);
				return r({ status: 'success' });

			} catch (error) {
				r(error.message);
			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateDeviceLogs':

			if (!rs) {
				r({ status: 'error', message: 'Missing parameters' });
				return;
			}
			await UpdateDeviceLogs(rs);
			break;

		// --------------------------------------------------------------------------------------------

		case 'GetTermsAndConditions':

			if (!lang) {
				r({ status: 'error', message: 'Language is missing' });
				return;
			}


			sql = 'SELECT * FROM terms_conditions WHERE lang = ? AND inactive = 0 ORDER BY id DESC LIMIT 1';
			query = await post(sql, [lang]);
			r({ status: 'success', data: query?.[0] });
			break;

		// --------------------------------------------------------------------------------------------

		case 'GetSurveyData':

			const surveyArray = [];
			sql = "SELECT * FROM exam_survey_question WHERE inactive = 0 ORDER BY rank";
			query = await post(sql);


			for (const item of query) {
				sql2 = "SELECT * FROM exam_survey_answers WHERE question_id = ?";
				query2 = await post(sql2, [item.id]);
				item['answers'] = query2;
				surveyArray.push(item);
			}
			r({ status: 'success', data: surveyArray });

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetTestInstruction':

			if (!test_type || !lang) {
				r({ status: 'error', message: 'Test Type or Language is missing' });
				return;
			}

			sql = `SELECT instruction FROM test_instructions WHERE test_type = ? AND lang = ? AND inactive = 0 ORDER BY id DESC LIMIT 1`;
			query = await post(sql, [test_type, lang]);
			r({ status: 'success', data: query?.[0] });

			break;
		// --------------------------------------------------------------------------------------------

		case 'GetTestTimetable':

			if (!test_type || !test_version) {
				r({ status: 'error', message: 'Test Type or Test Version is missing' });
				return;
			}

			sql = `SELECT
									sgrup AS question_group,
									
									IF(a.bolum <> '4',
										CONCAT((SELECT sira FROM sorular WHERE bolum = a.bolum AND sgrup = a.sgrup ORDER BY sira ASC LIMIT 1),
										' - ',
										(SELECT sira FROM sorular WHERE bolum = a.bolum AND sgrup = a.sgrup ORDER BY sira DESC LIMIT 1)),
										''
									) AS question_range,

									IF(a.bolum IN (1,2),
										(SELECT sure FROM seviye WHERE bolum = a.bolum AND baraj = a.seviye LIMIT 1),
										IF(a.bolum = 3,
											a.spsoru,
											a.spcevap
										)
									) AS level_duration
								FROM sorular a

								WHERE
									a.bolum = '${test_type}'
									AND a.grupid = '${test_version}'
									${test_type == 3 ? "" : "AND a.sgrup > 0 "}

								GROUP BY
									a.${test_type == 3 ? "sgrup" : "seviye"}
									
								ORDER BY
									a.sira
						`;

			query = await post(sql);
			r({ status: 'success', data: query });

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetTestStartingInstructions':

			if (!lang) {
				r({ status: 'error', message: 'Language is missing' });
				return;
			}

			sql = `SELECT title, subtitle, list, goodluck FROM test_starting_instructions WHERE lang = ? LIMIT 1`;
			query = await post(sql, [lang]);
			r({ status: 'success', data: query?.[0] });

			break;

		// --------------------------------------------------------------------------------------------

		case 'RegisterCandidate':

			if (!token) {
				r({ status: 'error', message: 'Token is missing' });
				return;
			}

			try {

				query = await post(`INSERT INTO adaylar SET ?`, {
					ad: name,
					soyad: lastname,
					tck: tckn || null,
					mail: email,
					dtarih: birthdate,
					cinsiyet: gender,
					nationality: nationality ? nationality.toLowerCase() : null,
					mother_language,
					education,
					occupation,
					dep: department,
					tel: `${area_code ? area_code : ''}${phone ? phone : ''}`,
					ktarih: moment.tz(TIMEZONE).format('YYYY-MM-DD HH:mm:ss'),
					tckn_verification
				});

				const InsertedID = query.insertId ?? null;

				if (InsertedID) {

					// Update the user_id in the oturum table
					query2 = await post(`UPDATE oturum SET userid = ? WHERE token = ?`, [InsertedID, token]);

					r({
						status: 'success',
						user_id: InsertedID,
						message: 'Candidate registered successfully',
						userid_updating: (query2.affectedRows > 0 ? true : false)
					});

				} else {
					r({ status: 'error', message: 'Candidate registration failed' });

				}

			} catch (error) {
				r({ status: 'error', message: error.message });

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'InsertSurvey':

			if (!token) r({ status: 'error', message: 'Token is missing' });

			// Insert Survey Answers
			const surveyAnswers = [];

			for (const key in req.body) {
				if (key.startsWith('answer_')) {
					if (req.body[key])
						surveyAnswers.push([
							token,
							key.replace('answer_', ''),
							req.body[key]
						]);
				}
			}

			if (surveyAnswers.length > 0) {

				sql = "INSERT INTO exam_user_survey_answers (token, question, answer) VALUES ?";
				query = await post(sql, [surveyAnswers]);

				api_result = {
					survey_inserted: query.affectedRows > 0,
					survey_answers: surveyAnswers
				}

			}

			// Update Survey Status
			if (job_position || test_take_reason) {

				sql2 = `UPDATE oturum SET ? WHERE token = ?`;
				query2 = await post(sql2, [{
					job_position: job_position,
					test_take_reason: test_take_reason
				}, token]);
				api_result.candidate_info_updated = query2.affectedRows > 0

			}

			r({ status: 'success', ...api_result });
			break;

		// --------------------------------------------------------------------------------------------

		case 'UpdateCandidatePhoto':

			if (!token || !file_name) {
				r({ status: 'error', message: 'Missing parameters: token or file_name is missing' });
				return;
			}

			try {
				// Update both photo (priority) and webcam (legacy) fields for compatibility
				query = await post(`UPDATE oturum SET photo = ?, webcam = ? WHERE token = ?`, [file_name, file_name, token]);
				r({ status: 'success' });

			} catch (error) {
				r({ status: 'error', message: error.message });
			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'GetQuestionData':

			r(await GetQuestionData(token, test_type, test_version, question_rank, dev_mode));
			break;

		// --------------------------------------------------------------------------------------------

		case 'GetDevData':

			if (!test_type) {
				r({ status: 'error', message: 'Missing parameters: test_type is missing' });
				return;
			}

			sql = `SELECT
							v.version,
							v.type,
							(CASE WHEN v.pasif IS NULL THEN true ELSE false END) AS active,
							v.bolum AS test_type,
							JSON_ARRAYAGG(
								JSON_OBJECT(
									'rank', s.sira,
									'question_id', s.id,
									'question_type', s.sorutipi,
									'question_group', s.sgrup,
									'level', s.seviye
								)
							) AS questions
						FROM version v
						LEFT JOIN sorular s ON v.version = s.grupid AND v.bolum = s.bolum
						WHERE
							v.bolum = ?
						GROUP BY v.version
						ORDER BY v.version ASC`;

			query = await post(sql, [test_type]);
			r({ status: 'success', data: query });

			break;

		// --------------------------------------------------------------------------------------------

		case 'SetDevData':

			if (!query_text) {
				r({ status: 'error', message: 'Missing parameters: query_text is missing' });
				return;
			}

			if (!query_text.includes('WHERE')) {
				r({ status: 'error', message: 'Query must include a WHERE clause' });
				return;
			}

			query = await post(query_text);
			r({ status: 'success', data: `Affected rows: ${query.affectedRows}` });
			break;

		// --------------------------------------------------------------------------------------------

		case 'SaveCandidateAnswer':

			if (!token || !question_rank || !question_id || !test_type || !test_version) {
				r({ status: 'error', message: 'Missing parameters: token, test_type, test_version, question is missing' });
				return;
			}

			try {

				let answer_saved = false;

				if (dev_mode) {
					answer_saved = true;
					api_result = { status: 'success', message: 'Answer saved successfully' };

				} else {

					if (test_type == 1 || test_type == 2) {

						//! Delete previous answers if exists:
						sql = `DELETE FROM yanitlar WHERE token = ? AND bolum = ? AND testid = ? AND soruid = ?`;
						query = await post(sql, [token, test_type, test_version, question_id]);

					}

					sql = `INSERT INTO yanitlar SET ?`;
					query = await post(sql, {
						token: token,
						bolum: test_type,
						testid: test_version,
						soru: question_rank,
						soruid: question_id,
						yanit: answer || null,
						tarih: now
					});
					answer_saved = query.affectedRows > 0;
					api_result = { answer_saved };

				}

				// Get the next question:
				if (answer_saved) {

					sql2 = `SELECT 
											sira AS next_question_rank,
											spsoru AS writing_time
											FROM sorular
											WHERE
												bolum = ?
												AND grupid = ?
												AND sira > ?
											ORDER BY
												sira ASC
												LIMIT 1`;

					query2 = await post(sql2, [test_type, test_version, question_rank]);
					row2 = query2[0] ?? null;

					const next_question_rank = row2?.next_question_rank ?? null;
					const writing_time = row2?.writing_time ?? null;

					api_result.status = 'success';
					api_result.next_question = next_question_rank;
					api_result.writing_time = writing_time;

					if (next_question_rank) {

						// Update the next question rank
						const question_column = (test_type == 2 ? 'soru2' : 'soru');
						query3 = await post(`UPDATE oturum SET ${question_column} = ? WHERE token = ?`, [next_question_rank, token]);
						api_result.next_question_updated = query3.affectedRows > 0;

					}

				}

			} catch (error) {
				api_result = { status: 'error', message: error.message };
			}

			r(api_result);
			break;

		// --------------------------------------------------------------------------------------------


		case 'FeedbackOps':

			if (!db_event || !token) {
				r({ status: 'error', message: 'Missing parameters: db_event or token is missing' });
				return;
			}

			let isFeedbackExists = false;
			if (db_event !== 'get') {
				sql = `SELECT COUNT(1) AS feedback_exists FROM test_feedbacks WHERE token = ?`;
				query = await post(sql, [token]);
				isFeedbackExists = query[0]?.feedback_exists > 0;
			}

			switch (db_event) {

				case 'submit_feedback_review':

					if (isFeedbackExists) {
						query2 = await post(`UPDATE test_feedbacks SET feedback = ? WHERE token = ?`, [rs.feedback, token]);
					} else {
						query2 = await post(`INSERT INTO test_feedbacks SET ?`, { token, feedback: rs.feedback });
					}

					if (query2.affectedRows > 0) {
						r({ status: 'success', message: 'Feedback saved successfully' });
					}

					break;

				case 'submit_feedback_experience':

					if (isFeedbackExists) {
						query3 = await post(`UPDATE test_feedbacks SET stars = ? WHERE token = ?`, [rs.experience, token]);
					} else {
						query3 = await post(`INSERT INTO test_feedbacks SET ?`, { token, stars: rs.experience });
					}

					if (query3.affectedRows > 0) {
						r({ status: 'success', message: 'Feedback updated/saved successfully' });
					}

					break;

				case 'get':
					query = await post(`SELECT stars AS experience, feedback FROM test_feedbacks WHERE token = ?`, [token]);
					r({ status: 'success', data: query[0] });

					break;

			}

			break;

		// --------------------------------------------------------------------------------------------

		case 'TestCompleted':

			if (!token || !test_type || !test_taker || !test_module) {
				r({ status: 'error', message: 'Missing parameters: token, test_type, test_taker or test_module is missing' });

				return;
			}

			try {

				const test_complete_column = (test_type == 2 ? 'bitti2' : 'bitti');
				const test_complete_date_column = (test_type == 2 ? 'bitis2' : 'bitis');

				query = await post(`UPDATE oturum SET 
												${test_complete_column} = 1,
												test_complete = 1,
												test_complete_date = ?,
												${test_complete_date_column} = ?
												WHERE token = ?`, [now, now, token]);

				if (query.affectedRows > 0) {

					// Send e-mail to the candidate:
					let email_message_id = null;
					let email_status = 'error';

					if (rs?.email_params) {
						const { messageId } = await sendEmail(rs.email_params);
						email_message_id = messageId;
						email_status = messageId ? 'success' : 'error';
					}

					api_result = {
						status: 'success',
						message: 'Test completed successfully',
						data: {
							send_email: {
								status: email_status,
								message_id: email_message_id
							}
						}
					};

					// Allocate to Assessor:
					if (rs?.allocate_assessor) {
						const allocate_assessor = await AllocateAssessor({ test_type, token, test_taker, test_module, company_id, dev_mode });
						api_result.data.allocation = allocate_assessor;
					}

				}

			} catch (error) {
				api_result = { status: 'error', message: error.message };
			}

			r(api_result);
			break;

		// --------------------------------------------------------------------------------------------

		// --------------------------------------------------------------------------------------------

		case 'TestAuth':
		case 'TestAuthSimple':

			try {

				let JWTToken;

				if (!token || token.length < 3) {
					r({ status: 'error', message: 'Invalid token' });
					return;
				}

				// For development: Mock authentication if database is not available
				const userEmail = req.body.email;
				const userToken = req.body.token;

				if (!userEmail || !userToken) {
					r({ status: 'error', message: 'Email and token are required' });
					return;
				}

				// Check for demo credentials
				if (userEmail === '<EMAIL>' && userToken === '141276') {
					// Create a JWT Token for demo user
					JWTToken = jwt.sign({
						email: userEmail,
						test_token: userToken,
						test_env: true,
						demo: true
					}, jwtSecret, {
						expiresIn: '24h'
					});

					const demoResponse = {
						status: 'success',
						jwt_token: JWTToken,
						email: userEmail,
						name: 'Demo Test Candidate',
						token: userToken,
						test_type: 1,
						test_completed: false,
						suspended: false,
						login_status: true,
						earliest_start_date: null,
						latest_start_date: null,
						attend_test: 1,
						candidate_id: 999,
						token_user_id: 999,
						first_login: 0,
						last_active: moment.tz(TIMEZONE).format('YYYY-MM-DD HH:mm:ss'),
						new_system: true,
						debug: true,
						question: 1,
						question2: 1,
						test_environment: true,
						part_name: 'Demo Test Module',
						demo_mode: true
					};

					console.log('Demo authentication successful:', demoResponse);
					r(demoResponse);
					return;
				}

				// Use the exact query from site.js for proper authentication
				const q = `SELECT
							a.start AS earliest_start_date,
							a.deadline AS latest_start_date,
							a.sinav AS test_type,
							a.bitti AS reading,
							a.bitti2 AS listening,
							a.kontrol AS security_check,
							a.aktif AS last_active,
							a.server AS new_system,
							a.debug AS debug,
							a.soru AS question,
							a.soru2 AS question2,
							a.userid AS token_user_id,
							(CASE
								WHEN a.userid = 0 THEN (SELECT userid FROM oturum WHERE mail = a.mail AND userid > 0 LIMIT 1)
								ELSE 0
							END) AS candidate_id,
							(CASE WHEN a.first_login IS NULL THEN 0 ELSE 1 END) AS first_login,
							(CASE
								WHEN a.sinav = 1 AND a.bitti = 1 AND a.bitti2 = 1 THEN 1
								WHEN a.sinav >= 3 AND a.bitti = 1 THEN 1
								ELSE 0
							END) AS test_completed,
							(CASE
									WHEN a.sinav = 1 AND a.bitti = 1 THEN 2
									ELSE a.sinav
							END) AS attend_test,
						(
							SELECT
								CONCAT(x2.ad, ' ', x2.soyad) AS fullname
							FROM
								oturum x1
							INNER JOIN
								adaylar x2 ON x2.id = x1.userid
							WHERE
								x1.mail = a.mail AND
								x1.userid > 0
							LIMIT 1
						) AS fullname
						FROM
							oturum a
						WHERE
							a.mail = ? AND
							a.token = ?
						LIMIT 1`;

				const result = await post(q, [email, token]);

				if (result.length === 0) {
					r({ status: 'error', message: 'Invalid token' });
					return;
				}

				const row = result[0];

				const test_type = row.test_type || 1;
				const test_completed = row.test_completed === 1; // Use actual completion status
				const suspended = false; // Always false for test environment
				const attend_test = row.attend_test || test_type;
				const candidate_id = row.candidate_id;
				const first_login = row.first_login;
				const new_system = true; // Always true for test environment
				const debug = true; // Always true for test environment
				const token_user_id = row.token_user_id ?? null;
				const question = row.question ?? 1;
				const question2 = row.question2 ?? 1;
				const email = row.email || '<EMAIL>';

				// Create a JWT Token with longer expiration for test environment
				JWTToken = jwt.sign({
					email: email,
					test_token: token,
					test_env: true
				}, jwtSecret, {
					expiresIn: '24h' // 24 hours for test environment
				});

				const response = {
					status: 'success',
					jwt_token: JWTToken,
					email: email,
					name: row.fullname || 'Test Candidate',
					token: token,
					test_type: test_type,
					test_completed: test_completed,
					suspended: suspended,
					login_status: true,
					earliest_start_date: null, // No restrictions in test environment
					latest_start_date: null, // No restrictions in test environment
					attend_test: attend_test,
					candidate_id: candidate_id,
					token_user_id: token_user_id,
					first_login: first_login,
					last_active: moment.tz(TIMEZONE).format('YYYY-MM-DD HH:mm:ss'),
					new_system: new_system,
					debug: debug,
					question: question,
					question2: question2,
					test_environment: true,
					part_name: `Test Module ${test_type}`
				};

				console.log('TestAuthSimple response:', response);

				r(response);

			} catch (error) {
				console.error('TestAuthSimple error:', error);
				r({ status: 'error', message: 'Authentication failed', error: error.message });
			}

			break;

		// --------------------------------------------------------------------------------------------

		default:
			r({ status: 'error', message: 'Invalid API operation' });
			break;

	}

	// ====================================================================================================

});

export default router;