import express from 'express';
import fetch from 'node-fetch';
import multer from 'multer';
import sharp from 'sharp'; // image processing

// Handlers
import { zoneKeys } from './functions.js';
import { importSpeedtestData } from './ops2/spreadsheet/import_speedtest.js';
import { UpdateCandidatePhoto } from './ops2/candidate_ops.js';

const router = express.Router();
const storage = multer.memoryStorage();
const upload = multer({
	storage: storage,
	limits: {
		fieldSize: 50 * 1024 * 1024 // 50 MB
	}
});
// ** ----------------------------------------------------------------------------------
// ** CANDIDATE AVATAR UPLOAD & DB UPDATE

router.post('/candidate-avatar', upload.single('file'), async (req, res) => {
	try {
		const file = req.file;
		const { token, zone = 'cepatest', max_width, max_height, quality = 90 } = req.body;

		if (!file) {
			return res.status(400).json({ status: 'error', message: 'No file uploaded' });
		}
		if (!token) {
			return res.status(400).json({ status: 'error', message: 'Candidate token is required' });
		}

		// Use token as folder, original filename as avatar filename
		const fileName = `${Date.now()}_${file.originalname}`;
		const filePath = `${token}`;
		const fullPath = `${filePath}/${fileName}`;

		// Upload to BunnyCDN
		const result = await uploadFileToCDN(file, zone, `/${fullPath}`, file.mimetype, {
			processImage: true,
			maxWidth: max_width ? parseInt(max_width) : undefined,
			maxHeight: max_height ? parseInt(max_height) : undefined,
			quality: parseInt(quality)
		});

		// Update DB with filename
		const dbUpdate = await UpdateCandidatePhoto({ token, photo_filename: fileName });

		return res.status(200).json({
			status: 'success',
			message: 'Avatar uploaded and candidate photo updated',
			data: {
				bunnycdn_url: `https://cepatest.b-cdn.net/${token}/${encodeURIComponent(fileName)}`,
				db: dbUpdate,
				uploaded: result
			}
		});
	} catch (error) {
		console.error('[CandidateAvatarUpload] Error:', error);
		res.status(500).json({ status: 'error', message: 'Upload failed', error: error.message });
	}
});
import express from 'express';
import fetch from 'node-fetch';
import multer from 'multer';
import sharp from 'sharp'; // image processing

// Handlers
import { zoneKeys } from './functions.js';
import { importSpeedtestData } from './ops2/spreadsheet/import_speedtest.js';

const router = express.Router();
const storage = multer.memoryStorage();
const upload = multer({
	storage: storage,
	limits: {
		fieldSize: 50 * 1024 * 1024 // 50 MB
	}
});

// ** ----------------------------------------------------------------------------------
// ** GENERIC FILE UPLOAD FUNCTION

/**
 * Generic file upload function that can be used across the system
 * @param {Object} file - Multer file object
 * @param {string} zone - CDN zone name
 * @param {string} filePath - Path where file should be stored
 * @param {string} contentType - MIME type of the file
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Upload result
 */
const uploadFileToCDN = async (file, zone, filePath, contentType, options = {}) => {
	try {
		const { processImage = false, maxWidth, maxHeight, quality = 100 } = options;

		let uploadBuffer = file.buffer;
		let finalContentType = contentType;

		// Process image if requested
		if (processImage && file.mimetype.startsWith('image/')) {
			try {
				const { width, height } = await sharp(file.buffer).metadata();

				// Calculate the scale ratio
				const maxW = maxWidth || Infinity;
				const maxH = maxHeight || Infinity;
				const ratio = Math.min(maxW / width, maxH / height, 1);

				// Resize the image
				uploadBuffer = await sharp(file.buffer)
					.resize({
						width: Math.round(width * ratio),
						height: Math.round(height * ratio),
						fit: 'inside',
						withoutEnlargement: true
					})
					.webp({ quality: quality })
					.toBuffer();

				finalContentType = 'image/webp';
			} catch (error) {
				console.error('[uploadFileToCDN] Image processing error:', error);
				// Continue with original file if image processing fails
			}
		}

		// Upload to BunnyCDN
		const url = `https://storage.bunnycdn.com/${zone}${filePath}`;
		const uploadOptions = {
			method: 'PUT',
			body: uploadBuffer,
			headers: {
				'AccessKey': zoneKeys[zone],
				'Content-Type': finalContentType,
			},
		};

		const response = await fetch(url, uploadOptions);

		if (!response.ok) {
			console.error('[uploadFileToCDN] CDN Error:', response.statusText);
			throw new Error(`CDN upload failed: ${response.statusText}`);
		}

		return {
			success: true,
			url: url,
			contentType: finalContentType,
			size: uploadBuffer.length
		};

	} catch (error) {
		console.error('[uploadFileToCDN] Error:', error);
		throw error;
	}
};

// ** ----------------------------------------------------------------------------------
// ** GENERIC FILE UPLOAD ENDPOINT

router.post('/upload', upload.single('file'), async (req, res) => {
	try {
		const file = req.file;
		const {
			zone,
			file_path,
			file_name,
			content_type,
			process_image = false,
			max_width,
			max_height,
			quality = 100
		} = req.body;

		if (!file) {
			return res.status(400).json({ status: 'error', message: 'No file uploaded' });
		}

		if (!zone || !file_path) {
			return res.status(400).json({ status: 'error', message: 'Zone and file_path are required' });
		}

		// Generate file name if not provided
		const fileName = file_name || `${Date.now()}_${file.originalname}`;
		const fullPath = `${file_path}/${fileName}`;

		// Determine content type
		let contentType = content_type || file.mimetype;
		if (!contentType || contentType === 'application/octet-stream') {
			const mimeTypes = {
				'pdf': 'application/pdf',
				'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				'xls': 'application/vnd.ms-excel',
				'csv': 'text/csv',
				'jpg': 'image/jpeg',
				'jpeg': 'image/jpeg',
				'png': 'image/png'
			};
			const fileExtension = file.originalname.split('.').pop();
			contentType = mimeTypes[fileExtension.toLowerCase()] || 'application/octet-stream';
		}

		// Upload file
		const result = await uploadFileToCDN(file, zone, fullPath, contentType, {
			processImage: process_image === 'true',
			maxWidth: max_width ? parseInt(max_width) : undefined,
			maxHeight: max_height ? parseInt(max_height) : undefined,
			quality: parseInt(quality)
		});

		// Return success with file details
		res.status(200).json({
			status: 'success',
			data: {
				file_name: fileName,
				file_path: fullPath,
				file_size: file.size,
				file_type: file.originalname.split('.').pop(),
				mime_type: contentType,
				original_name: file.originalname,
				uploaded_size: result.size,
				uploaded_content_type: result.contentType
			},
			message: 'File uploaded successfully'
		});

	} catch (error) {
		console.error('[GenericUpload] Error:', error);
		res.status(500).json({
			status: 'error',
			message: 'Upload failed',
			error: error.message
		});
	}
});

// ** ----------------------------------------------------------------------------------
// ** SPEEDTEST UPLOAD

router.post('/speedtest', upload.single('file'), async (req, res) => {
	const file = req.file;
	if (!file) return res.status(400).json({ status: 'error', message: 'No file uploaded' });
	res.status(200).json(await importSpeedtestData(file));
});

// ** ----------------------------------------------------------------------------------
// ** IMAGE UPLOAD (existing functionality - now using generic function)

router.post('*', upload.single('file'), async (req, res, next) => {
	try {
		const file = req.file;
		const {
			zone,
			file_name,
			file_path,
			max_width,
			max_height,
			quality = 100
		} = req.body;

		if (!file) {
			errorHandler(res, 'No file uploaded');
		} else if (!zone) {
			errorHandler(res, 'No zone specified');
		} else if (!file_name) {
			errorHandler(res, 'No file name specified');
		} else if (!file_path) {
			errorHandler(res, 'No file path specified');
		}

		const fullPath = `${file_path}/${file_name}`;

		// Upload using generic function with image processing
		await uploadFileToCDN(file, zone, fullPath, 'image/webp', {
			processImage: true,
			maxWidth: max_width ? parseInt(max_width) : undefined,
			maxHeight: max_height ? parseInt(max_height) : undefined,
			quality: parseInt(quality)
		});

		res.status(200).json({
			status: 'success',
			message: 'Image uploaded and processed successfully'
		});

	} catch (error) {
		console.error(error);
		res.status(500).json({ status: 'failure', message: 'Internal Server Error' });
	}
});

function errorHandler(res, message) {
	res.status(400).json({ status: 'error', message });
}

export default router;
