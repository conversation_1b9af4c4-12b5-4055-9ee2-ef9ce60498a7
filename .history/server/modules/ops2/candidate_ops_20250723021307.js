import { post } from '../mysql/mysql.js';
import { startTimer } from '../../utils/performance-monitor.js';

// ** -------------------------------------------------------------------------------------------------------------------------------
// ** Get Tests by Overall Group */

export async function GetTestsByOverallGroup({ email, overall_group, include_test_counts }) {

  if (!email) {
    return {
      status: 'error',
      message: 'Email is required'
    };
  }

  try {

    // If include_test_counts is true, return overall groups with test counts
    if (include_test_counts) {
      const sql = `SELECT
                      o.overall_group,
                      COUNT(*) as test_count,
                      MAX(CASE WHEN sc.status = 3 THEN 1 ELSE 0 END) as has_suspected
                   FROM oturum o
                   LEFT JOIN security_checks sc ON o.token = sc.token
                   WHERE o.mail = ?
                     AND o.overall_group > 0
                   GROUP BY o.overall_group
                   ORDER BY o.overall_group`;

      const result = await post(sql, [email]);

      return {
        status: 'success',
        data: result
      }
    }

    // Original functionality - get tests for specific overall group
    if (!overall_group) {
      return {
        status: 'error',
        message: 'Overall group is required when include_test_counts is false'
      };
    }

    const sql = `SELECT
                    o.token,
                    o.sinav AS test_type,
                    o.test_complete AS test_completed,
                    sc.status AS security_check,
                    wy.complete AS assessment_completed,
                    CASE WHEN o.sinav = 1 THEN 1 ELSE wy.complete END AS assessment_completed
                 FROM oturum o
                 LEFT JOIN security_checks sc ON o.token = sc.token
                 LEFT JOIN writingyorum wy ON wy.token = o.token
                  WHERE
                    o.mail = ?
                    AND o.overall_group = ?
                  `;
    const result = await post(sql, [email, overall_group]);

    return {
      status: 'success',
      data: result
    }

  } catch (error) {

    return {
      status: 'error',
      message: error.message
    }

  }

}



// ** -------------------------------------------------------------------------------------------------------------------------------
//** Get Candidate Info (Brief) */

export async function GetCandidateInfo({ token, email, overall_group }) {

  if (!token && !email) {
    return {
      status: 'error',
      message: 'Token or email is required'
    };
  }

  let SUB_QUERY_1 = '';
  let WHERE = `AND a.token = '${token}'`;

  if (overall_group) {

    console.log('Overall Group:', overall_group);

    const CefrLevel = (col, score_no = 1) => {
      return `SELECT s.${col}
              FROM skala s
              WHERE
                s.skala = 2
                AND s.bolum = 1
                AND s.skor1 <= ROUND(a3.score_${score_no})
                AND s.skor2 >= ROUND(a3.score_${score_no})
              LIMIT 1`;
    }

    SUB_QUERY_1 = `-- //! -------------------------------------------------------------------------------------------------
                  
             ,(
                 SELECT JSON_ARRAYAGG(
                   JSON_OBJECT(
                     'token', a2.token,
                     'test_type', a2.sinav,
                     'test_version', a2.testid,
                     'complete_date', IFNULL(a2.bitis2, a2.bitis),
                     'scores', JSON_OBJECT(
                       'score1', a3.score_1,
                       'score2', a3.score_2,
                       'cefr1', (${CefrLevel('cefr', 1)}),
                       'cefr2', (${CefrLevel('cefr', 2)}),
                       'level1', (${CefrLevel('seviye', 1)}),
                       'level2', (${CefrLevel('seviye', 2)}),
                       'rubrics', (
                         SELECT JSON_ARRAYAGG(
                           JSON_OBJECT(
                             'rubric_id', r.rid,
                             'rubric_title', r.kriter,
                             'task', r.task,
                             'max_score', r.max_score,
                             'score', s.puan
                           )
                         )
                         FROM (
                             SELECT
                                 r.rid,
                                 r.kriter,
                                 r.task,
                                 r.bolum,
                                 MAX(IFNULL(r.p2, r.p1)) AS max_score
                             FROM rubrics r
                             WHERE
                                 r.bolum IN (3, 4)
                             GROUP BY r.rid, r.kriter, r.task, r.bolum
                         ) AS r
                         INNER JOIN rubrics_title rt ON r.rid = rt.id
                         LEFT JOIN sonuc s 
                           ON r.rid = s.rid 
                         WHERE 
                           s.token IN (SELECT token FROM oturum WHERE token = a2.token)  -- ✅ Correct reference
                         ORDER BY r.task, rt.rate
                       )
                     )
                   )
                 )
                 FROM oturum a2
                 INNER JOIN candidate_results a3 ON a2.token = a3.token
                 WHERE
                   a2.mail = '${email}' 
                   AND a2.overall_group = ${overall_group}
             ) AS tests


                   -- //! -------------------------------------------------------------------------------------------------
                  ,(
                      SELECT JSON_OBJECT(
                        'score', @total_score := ROUND(SUM(
                          IF(a2.sinav = 1, (a3.score_1 + a3.score_2) / 2,
                          IF(a2.sinav IN (3, 4), a3.score_1, 0))
                        ) / 3),

                        'cefr', (
                          SELECT s.cefr
                          FROM skala s
                          WHERE s.skala = 2 
                          AND s.bolum = 1
                          AND s.skor1 <= @total_score
                          AND s.skor2 >= @total_score
                          LIMIT 1
                        ),

                        'level', (
                          SELECT s.seviye
                          FROM skala s
                          WHERE s.skala = 2 
                          AND s.bolum = 1
                          AND s.skor1 <= @total_score
                          AND s.skor2 >= @total_score
                          LIMIT 1
                        )
                      )
                      FROM oturum a2
                      INNER JOIN candidate_results a3 ON a2.token = a3.token
                      WHERE a2.mail = '${email}' 
                      AND a2.overall_group = ${overall_group}
                  ) AS overall

                   -- //! -------------------------------------------------------------------------------------------------
                   
   `;

    WHERE = `AND a.overall_group = ${overall_group}
               AND a.mail = '${email}'
               AND a.sinav = 1
    `;
  }

  try {

    const sql = `SELECT
                    a.token,
                    a.sinav AS test_type,
                    a.mail AS email,
                    a.overall_group,
                    a.assign_date,
                    a.kurum AS company_id,
                    a.test_complete,

                    (CASE WHEN a.test_complete = 1 AND a.sinav >= 3 THEN
                      (SELECT IF(COUNT(*) > 0, 1, 0)
                      FROM writingyorum wy
                      WHERE wy.token = a.token AND wy.complete = 1)
                    ELSE 1 END) AS assessment_complete,

                    c.kurum AS company_name,
                    c.logo AS company_logo,
                    IFNULL(a.bitis2, a.bitis) AS complete_date,
                    c.score_system AS company_score_system,
                    a.webcam AS avatar,
                    a.photo AS photo,
                    -- Priority logic: photo field takes precedence over webcam field
                    COALESCE(a.photo, a.webcam) AS primary_avatar,
                    b.id AS user_id,
                    b.dtarih AS birthdate,
                    b.ad AS name,
                    b.soyad AS lastname
                    ${SUB_QUERY_1}
                  FROM
                    oturum a
                  LEFT JOIN
                    adaylar b ON a.userid = b.id
                  LEFT JOIN
                    kurumlar c ON a.kurum = c.id
                  WHERE 1=1
                  ${WHERE}
                  LIMIT 1`;

    const result = await post(sql, [token]);
    // console.log('SQL Result:', JSON.stringify(result, null, 2));
    return result[0] ?? null;

  } catch (error) {

    return {
      status: 'error',
      message: error.message
    };
  }

}

//** --------------------------------------------------------------------------------------------------------------------------------
//** Edit Candidate  */

export async function EditCandidate(params) {

  if (!params) return { status: 'error', message: 'Params are required' };

  const { id, name, lastname, phone, position, gender, birthdate } = params;

  try {

    const sql = `UPDATE adaylar SET ad = ?, soyad = ?, tel = ?, dep = ?, cinsiyet = ?, dtarih = ? WHERE id = ?`;
    const querry = await post(sql, [name, lastname, phone, position, gender, birthdate, id]);

    return {
      status: 'success',
      message: 'Candidate information updated successfully'
    };

  } catch (error) {

    return {
      status: 'error',
      message: error.message
    };
  }

}

//** --------------------------------------------------------------------------------------------------------------------------------
//** Get Candidate Information  */

export async function GetCandidate({ token }) {

  if (!token) {
    return {
      status: 'error',
      message: 'Token is required'
    };
  }


  const candidate = await GetCandidateByToken(token);
  const email = candidate?.email;

  if (!candidate) return {
    status: 'error',
    message: `${token} is not a valid token or assigned to a candidate.`
  };

  const tests = await GetCandidateTests(email);

  return {
    status: 'success',
    data: {
      candidate,
      tests,
    }
  };

}

//** --------------------------------------------------------------------------------------------------------------------------------
//** Find Candidate with Test Token  */

export async function GetCandidateByToken(token) {

  if (!token) return null;

  try {

    const sql = `SELECT
                    IFNULL(o2.mail, o1.mail) AS email,
                    o2.userid AS user_id,
                    COALESCE(b.ad, o2.name) AS name,
                    COALESCE(b.soyad, o2.lastname) AS lastname,
                    IFNULL(CONCAT('*******', RIGHT(b.tck, 4)), '') AS tckn,
                    b.dep AS position,
                    b.dtarih AS birthdate,
                    b.tel AS phone,
                    b.cinsiyet AS gender,
                    b.ktarih AS registration_date,
                    COALESCE(o2.photo, o1.photo) AS photo
                FROM
                  oturum o1
                LEFT JOIN
                  oturum o2 ON o1.mail = o2.mail AND o2.userid > 0
                LEFT JOIN
                  adaylar b ON o2.userid = b.id
                WHERE
                  o1.token = ?
                  AND LENGTH(o1.mail) > 5
                LIMIT 1`;

    const querry = await post(sql, [token]);
    return querry[0] ?? null;

  } catch (error) {

    return {
      status: 'error',
      message: 'Failed to fetch candidate data',
      error: error.message
    };

  }

}




//** --------------------------------------------------------------------------------------------------------------------------------
//** Get Candidate Tests  */

async function GetCandidateTests(email) {

  try {

    const sql = `SELECT
                    a.token,
                    MAX(a.userid) AS user_id,
                    MAX(a.kurum) AS company_id,
                    MAX(b.kurum) AS company_name,
                    MAX(b.logo) AS company_logo,
                    MAX(b.score_system) AS company_score_system,
                    MAX(a.webcam) AS avatar,
                    MAX(a.photo) AS photo,
                    MAX(a.server) AS server,
                    MAX(a.debug) AS debug,
                    MAX(a.overall_group) AS overall_group,
                    MAX(a.sinav) AS test_type,
                    MAX(a.testid) AS test_version,
                    MAX(a.soru) AS question,
                    MAX(a.soru2) AS question2,
                    MAX(a.sure) AS time,
                    MAX(a.sure2) AS time2,
                    MAX(a.test_complete) AS test_complete,
                    MAX(IFNULL(a.bitis2, a.bitis)) AS complete_date,
                    MAX(a.webcam_off) AS webcam_off,
                    MAX(a.fullscreen_off) AS fullscreen_off,
                    MAX(a.aktif) AS last_active,
                    MAX(a.assign_date) AS assign_date,
                    MAX(a.template_id) AS template_id,
                    MAX(a.start) AS start_date,
                    MAX(a.deadline) AS deadline,
                    MAX(a.do_not_allocate) AS do_not_allocate,
                    MAX(a.report_receivers) AS report_receivers,
                    MAX(a.demo) AS demo,
                    MAX(a.bitti) AS completed1,
                    MAX(a.bitti2) AS completed2,
                    MAX(a.bitis) AS completed_date1,
                    MAX(a.bitis2) AS completed_date2,

                    (
                    SELECT
                      JSON_OBJECT(
                        'token', a.token,
                        'test_type', MAX(a.sinav),
                        'score_1', MAX(cs.score_1),
                        'score_2', MAX(cs.score_2)
                      )
                    ) AS test_score,

                    CASE
                      WHEN MAX(a.sinav) = 1 AND MAX(a.bitti) = 1 AND MAX(a.bitti2) = 1 THEN 1
                      WHEN MAX(a.sinav) IN (3, 4) THEN
                      (
                          SELECT IF(COUNT(*) > 0, 1, 0)
                          FROM writingyorum wy
                          WHERE wy.token = a.token AND wy.complete = 1
                      )
                      ELSE
                      (
                          SELECT IF(COUNT(*) > 0, 1, 0)
                          FROM writingyorum wy
                          WHERE wy.token = a.token AND wy.complete = 1
                      )
                    END AS assessment_completed,

                    MAX(sc.status) AS security_check,
                    MAX(CASE WHEN sc.status IS NOT NULL THEN
                      CASE WHEN u.soyad IS NULL THEN u.ad ELSE CONCAT(u.ad, ' ', u.soyad) END
                      ELSE NULL END) AS security_check_completed_by,
                    MAX(sc.date) AS security_check_completed_date

                    FROM
                      oturum a
                    LEFT JOIN kurumlar b ON a.kurum = b.id
                    LEFT JOIN candidate_results cs ON a.token = cs.token
                    LEFT JOIN security_checks sc ON a.token = sc.token
                    LEFT JOIN users u ON sc.staff = u.id
                    WHERE
                      a.mail = ?
                    GROUP BY
                      a.token
                    ORDER BY
                      CASE
                        WHEN MAX(a.overall_group) IS NULL OR MAX(a.overall_group) = 0 THEN MAX(a.sinav)
                        ELSE MAX(a.overall_group)
                      END,
                      MAX(a.sinav) ASC`;

    const query = await post(sql, [email]);

    // ** Get CEFR Scale:
    const sql2 = `SELECT cefr, seviye AS cefr_level, skor1 AS min_score, skor2 AS max_score FROM skala WHERE bolum = 1 AND skala = 2 ORDER BY cefr_level ASC`;
    const query2 = await post(sql2);

    return query.map(row => ({
      ...row,
      cefr_scale: query2
    }));

  } catch (error) {
    return {
      status: 'error',
      message: 'Failed to fetch candidate tests',
      error: error.message
    };
  }

}

//* --------------------------------------------------------------------------------------------------------------------------------
//* Get Test Takers 
//* --------------------------------------------------------------------------------------------------------------------------------

export async function GetTestTakers(params) {

  if (!params) return { status: 'error', message: 'Params are required' };

  const { page = 1, pageSize = 25, keyword, company_id, test_counts } = params || {};

  // Validate and sanitize inputs
  const currentPage = Math.max(1, Number(page));
  const validPageSize = Math.min(100, Math.max(1, Number(pageSize))); // Cap at 100
  const offset = (currentPage - 1) * validPageSize;

  try {
    // Start performance monitoring
    const timer = startTimer('GetTestTakers', { page, pageSize, keyword, company_id, test_counts });

    // Build WHERE conditions with parameterized queries for security
    const whereConditions = [];
    const queryParams = [];

    // Keyword search with parameterized queries - search by email, name, lastname, or token
    if (keyword && keyword.trim().length >= 3) {
      const searchTerm = `%${keyword.trim()}%`;
      whereConditions.push(`(COALESCE(a.ad, '') LIKE ? OR COALESCE(a.soyad, '') LIKE ? OR o.mail LIKE ? OR o.token LIKE ?)`);
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Company filter
    if (company_id && Number(company_id) > 0) {
      whereConditions.push(`o.kurum = ?`);
      queryParams.push(Number(company_id));
    }

    // Test counts filter - will be handled differently in main vs count query
    const testCountsFilter = test_counts && Number(test_counts) > 0 ? Number(test_counts) : null;

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Updated query to ensure uniqueness based on email from oturum table
    // Returns single token per email and total count of all tokens for that email
    // Compatible with sql_mode=only_full_group_by
    const mainQuery = `
      SELECT
        COALESCE(first_user.id, 0) AS id,
        COALESCE(first_user.ad, SUBSTRING_INDEX(email_data.mail, '@', 1)) AS name,
        COALESCE(first_user.soyad, '') AS lastname,
        email_data.mail AS email,
        email_data.token,
        email_data.avatar,
        email_data.photo,
        email_data.server,
        email_data.total_tokens,
        COALESCE(company_names.companies, '') AS companies
      FROM (
        SELECT
          o.mail,
          MIN(o.token) AS token,
          MIN(o.webcam) AS avatar,
          MIN(o.photo) AS photo,
          MIN(o.server) AS server,
          COUNT(*) AS total_tokens,
          MIN(o.userid) AS first_userid,
          MIN(COALESCE(a.ad, SUBSTRING_INDEX(o.mail, '@', 1))) AS sort_name,
          MIN(COALESCE(a.soyad, '')) AS sort_lastname
        FROM oturum o
        LEFT JOIN adaylar a ON o.userid = a.id
        WHERE o.mail IS NOT NULL
          AND LENGTH(o.mail) > 5
          AND o.mail LIKE '%@%'
        ${whereClause.replace('WHERE ', 'AND ')}
        GROUP BY o.mail
        ${testCountsFilter ? `HAVING total_tokens >= ?` : ''}
        ORDER BY sort_name ASC, sort_lastname ASC
        LIMIT ? OFFSET ?
      ) email_data
      LEFT JOIN adaylar first_user ON email_data.first_userid = first_user.id
      LEFT JOIN (
        SELECT
          o.mail,
          GROUP_CONCAT(DISTINCT k.kurum ORDER BY k.kurum SEPARATOR ', ') AS companies
        FROM oturum o
        INNER JOIN kurumlar k ON o.kurum = k.id
        WHERE o.mail IS NOT NULL
        GROUP BY o.mail
      ) company_names ON email_data.mail = company_names.mail
      ORDER BY COALESCE(first_user.ad, SUBSTRING_INDEX(email_data.mail, '@', 1)) ASC, COALESCE(first_user.soyad, '') ASC
    `;

    // Updated count query to count unique emails
    const countQuery = `
      SELECT COUNT(DISTINCT email_counts.mail) AS total
      FROM (
        SELECT
          o.mail,
          COUNT(*) AS email_token_count
        FROM oturum o
        LEFT JOIN adaylar a ON o.userid = a.id
        WHERE o.mail IS NOT NULL
          AND LENGTH(o.mail) > 5
          AND o.mail LIKE '%@%'
        ${whereClause.replace('WHERE ', 'AND ')}
        GROUP BY o.mail
        ${testCountsFilter ? `HAVING email_token_count >= ?` : ''}
      ) email_counts
    `;

    // Add parameters in correct order: search params, test count filter, pagination
    const finalQueryParams = [...queryParams];
    if (testCountsFilter) {
      finalQueryParams.push(testCountsFilter);
    }
    finalQueryParams.push(validPageSize, offset);

    const countQueryParams = [...queryParams];
    if (testCountsFilter) {
      countQueryParams.push(testCountsFilter);
    }

    // Execute queries in parallel for better performance
    console.log('[GetTestTakers] Executing queries with params:', {
      searchParams: { keyword, company_id, test_counts },
      pagination: { page: currentPage, pageSize: validPageSize },
      testCountsFilter,
      finalQueryParams,
      countQueryParams
    });

    const queryStartTime = Date.now();
    const [countResult, dataResult] = await Promise.all([
      post(countQuery, countQueryParams),
      post(mainQuery, finalQueryParams)
    ]);
    const queryEndTime = Date.now();

    console.log('[GetTestTakers] Query execution time:', `${queryEndTime - queryStartTime}ms`);

    // Process results to match expected format
    const processedData = dataResult.map(row => ({
      ...row,
      companies: row.companies ? row.companies.split(', ') : []
    }));

    const result = {
      status: 'success',
      message: 'Test takers fetched successfully',
      data: {
        data: processedData,
        total: countResult[0]?.total || 0
      },
    };

    // End performance monitoring
    timer.end(result);

    return result;

  } catch (error) {
    console.error('[GetTestTakers] Database error:', error);
    return {
      status: 'error',
      message: 'Failed to fetch test takers',
      error: error.message
    };
  }

}

//** --------------------------------------------------------------------------------------------------------------------------------

export async function GetSpeedTestResults(params) {

  const { page, pageSize = 100, token } = params || {};

  // Ensure valid pagination values
  const currentPage = Math.max(1, Number(page));
  const validPageSize = Math.max(1, Number(pageSize));

  try {

    const baseSql = `SELECT * FROM candidate_sys_data ${token ? `WHERE token = '${token}'` : ''} ORDER BY create_date DESC`;
    const limitQuery = `LIMIT ${validPageSize} OFFSET ${(currentPage - 1) * validPageSize}`;

    // Improved error handling for SQL execution
    const [totalCounts, result] = await Promise.all([
      post(`SELECT COUNT(*) AS total FROM (${baseSql}) AS t`),
      post(`${baseSql} ${limitQuery}`)
    ]);

    return {
      status: 'success',
      message: 'Speed test results fetched successfully',
      data: {
        data: result,
        total: totalCounts[0]?.total || 0
      },
    };

  } catch (error) {

    return {
      status: 'error',
      message: 'Failed to fetch speed test results',
      error: error.message
    };

  }

}

// ** -------------------------------------------------------------------------------------------------------------------------------
// ** Update Candidate Photo */

export async function UpdateCandidatePhoto({ token, photo_filename }) {

  if (!token) {
    return {
      status: 'error',
      message: 'Token is required'
    };
  }

  if (!photo_filename) {
    return {
      status: 'error',
      message: 'Photo filename is required'
    };
  }

  try {

    // Update the photo field in oturum table
    const sql = `UPDATE oturum SET photo = ? WHERE token = ?`;
    const result = await post(sql, [photo_filename, token]);

    if (result.affectedRows > 0) {
      return {
        status: 'success',
        message: 'Candidate photo updated successfully',
        data: {
          token: token,
          photo_filename: photo_filename,
          updated_rows: result.affectedRows
        }
      };
    } else {
      return {
        status: 'error',
        message: 'No candidate found with the provided token'
      };
    }

  } catch (error) {

    console.error('UpdateCandidatePhoto Error:', error);

    return {
      status: 'error',
      message: 'Failed to update candidate photo',
      error: error.message
    };

  }

}