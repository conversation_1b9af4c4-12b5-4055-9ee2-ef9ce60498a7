import React from 'react';

// Types
import type { CandidateInfoProps } from '../../types';

// UI
import { Skeleton, Spin, Dropdown } from 'antd';

// Utils
import { GENDERS } from '@/utils/consts';
import { handleCopy } from '@/utils/helpers';

// Hooks
import { useNotification } from '@/hooks/useNotification';
import { useDateFormatting } from '@/hooks/useDateFormatting';

// Store
import { useModalStore } from '@/store/useModalStore';
import { useCandidateStore } from '@/store/useCandidateStore';

// Hooks
import { useCandidateSelectors } from '@/hooks/useCandidateSelectors';

// Components
import TableRow from '@/components/TableRow';
import Avatar from '@/components/Avatar';

// Icons
import { FaRegAddressCard, FaPhoneAlt } from 'react-icons/fa';
import { BsGenderAmbiguous, BsCalendar3 } from 'react-icons/bs';
import { CgWorkAlt } from 'react-icons/cg';
import { FaUserEdit } from "react-icons/fa";
import { MdContentCopy } from 'react-icons/md';
import { AiOutlineUpload, AiOutlineMore } from 'react-icons/ai';

// Lazy Load
const EditCandidate = React.lazy(() => import('./EditCandidate'));
const UploadAvatar = React.lazy(() => import('./UploadAvatar'));

const CandidateInfo: React.FC<CandidateInfoProps> = ({ selectedTest }) => {
  // Store
  const { setModal, showModal } = useModalStore();
  const { candidate } = useCandidateStore();
  const token = useCandidateStore(state => state.token);
  const { selectedTest: currentSelectedTest } = useCandidateSelectors();

  // Hooks
  const { showSuccess, contextHolder } = useNotification();
  const { formatBirthDate } = useDateFormatting();
  const emailCopyRef = React.useRef<HTMLDivElement>(null);

  // Memoized values
  const { isCandidate, title } = React.useMemo(() => ({
    isCandidate: candidate?.user_id ? candidate.user_id > 0 : false,
    title: candidate?.user_id ? 'Candidate' : 'Token'
  }), [candidate?.user_id]);

  const handleEditClick = React.useCallback(() => {
    setModal({
      content: (
        <React.Suspense fallback={<Spin />}>
          <EditCandidate />
        </React.Suspense>
      ),
    });
    showModal();
  }, [setModal, showModal]);

  const handleEmailCopy = React.useCallback(() => {
    if (emailCopyRef.current) {
      handleCopy(emailCopyRef);
      showSuccess('Success', 'Email copied to clipboard.');
    }
  }, [showSuccess]);

  const emailContent = React.useMemo(() => {
    if (!candidate?.email) return null;

    return (
      <div className="d-flex align-items-center">
        <a href={`mailto:${candidate.email}?subject=[${selectedTest?.token}] Your ${selectedTest?.test_name || selectedTest?.name} Test`}>
          <span ref={emailCopyRef}>{candidate.email}</span>
        </a>
        <span className="ms-1 text-primary pointer" onClick={handleEmailCopy}>
          <MdContentCopy className="text-secondary" />
        </span>
      </div>
    );
  }, [candidate?.email, selectedTest?.token, selectedTest?.test_name, selectedTest?.name, handleEmailCopy]);

  const birthDateContent = React.useMemo(() => {
    if (!candidate?.birthdate) return null;
    return formatBirthDate(candidate.birthdate);
  }, [candidate?.birthdate, formatBirthDate]);

  const genderLabel = React.useMemo(() =>
    GENDERS[candidate?.gender || 0]?.label || '',
    [candidate?.gender]
  );

  // Avatar dropdown handlers
  const handleUploadPhoto = React.useCallback(() => {
    setModal({
      content: (
        <React.Suspense fallback={<Spin />}>
          <UploadAvatar
            token={token || ''}
            onUploadSuccess={() => {
              // Refresh candidate data after successful upload
              // The UploadAvatar component will handle this internally
            }}
          />
        </React.Suspense>
      ),
    });
    showModal();
  }, [setModal, showModal, token]);

  // Avatar dropdown menu items
  const avatarMenuItems = React.useMemo(() => [
    {
      key: 'upload-photo',
      label: 'Upload Photo',
      icon: <AiOutlineUpload />,
      onClick: handleUploadPhoto,
    },
  ], [handleUploadPhoto]);

  // Candidate display name with token and test module
  const candidateDisplayName = React.useMemo(() => {
    const name = candidate?.name
      ? `${candidate.name} ${candidate.lastname || ''}`.trim()
      : candidate?.email || 'Unknown Candidate';

    const testModule = currentSelectedTest?.name || currentSelectedTest?.test_name;
    const tokenInfo = testModule ? `${token} (${testModule})` : token;

    return `${name} - ${tokenInfo}`;
  }, [candidate?.name, candidate?.lastname, candidate?.email, token, currentSelectedTest?.name, currentSelectedTest?.test_name]);



  if (!candidate) {
    return (
      <div className="mt-3 mt-lg-4 mx-0 mx-lg-2">
        <div className="fs-5 fw-bold mb-1">Loading Information...</div>
        <Skeleton active={true} paragraph={{ rows: 5 }} />
      </div>
    );
  }

  return (
    <>
      {contextHolder}

      <div className="mt-3 mt-lg-4 mx-0 mx-lg-2">
        <div className="d-flex align-items-center justify-content-between mb-3">
          <div className="fs-5 fw-bold">
            {candidateDisplayName}
          </div>

          <button
            type="button"
            onClick={handleEditClick}
            className="fw-semibold px-4 border border-light py-2 rounded-3 bg-transparent"
          >
            <FaUserEdit size={20} className="me-1" />
            Edit {title}
          </button>
        </div>

        <div className="row align-items-start">
          <div className="col-lg-3 col-md-4 col-12 text-center mb-3 mb-lg-0">
            <div className="position-relative d-inline-block">
              <Avatar
                token={token}
                avatar={currentSelectedTest?.avatar || ''}
                photo={candidate?.photo}
                preview={true}
                size={150}
                rounded={true}
              />
              <Dropdown
                menu={{ items: avatarMenuItems }}
                placement="bottomRight"
                trigger={['click']}
              >
                <button
                  type="button"
                  className="position-absolute top-0 end-0 btn btn-sm btn-light rounded-circle shadow-sm"
                  style={{
                    width: '32px',
                    height: '32px',
                    transform: 'translate(25%, -25%)'
                  }}
                  title="Photo options"
                  aria-label="Photo options"
                >
                  <AiOutlineMore size={14} />
                </button>
              </Dropdown>
            </div>
          </div>

          <div className="col-lg-9 col-md-8 col-12">
          <TableRow
            left="TCKN/Passport"
            right={candidate.tckn}
            icon={<FaRegAddressCard className="info__Icon" />}
          />

          <TableRow
            left="E-mail"
            right={emailContent}
          />

          {isCandidate && (
            <>
              <TableRow
                left="Phone"
                right={candidate.phone}
                icon={<FaPhoneAlt className="info__Icon" />}
              />

              <TableRow
                left="Gender"
                right={genderLabel}
                icon={<BsGenderAmbiguous className="info__Icon" />}
              />

              <TableRow
                left="Date of Birth"
                right={birthDateContent}
                icon={<BsCalendar3 className="info__Icon" />}
              />

              {candidate.position && (
                <TableRow
                  left="Position/Title"
                  right={candidate.position}
                  icon={<CgWorkAlt className="info__Icon" />}
                />
              )}
            </>
          )}
          </div>
        </div>
      </div>
    </>
  );
};

export default React.memo(CandidateInfo);