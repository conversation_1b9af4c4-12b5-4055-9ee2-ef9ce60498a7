/* eslint-disable react-hooks/exhaustive-deps */

// React
import React from 'react';
import { useNavigate } from 'react-router';

// UI
import { Spin, Table, Form, Input, Button, Select, Card, Space, Skeleton, Tag, Tooltip, Empty } from 'antd';

// NPM
import { useQuery } from '@tanstack/react-query';
import Swal from 'sweetalert2';

// Hook
import useCompanies from '@/hooks/useCompanies';

// Utils
import { api } from '@/utils/api';
import { uppCase } from '@/utils/helpers';
import { HOME_PATH } from '@/utils/consts';

// Icons
import { HiUsers } from "react-icons/hi";
import { AiOutlineSearch, AiOutlineReload } from "react-icons/ai";
import { UserOutlined, TeamOutlined, FileTextOutlined } from '@ant-design/icons';

// Lazy Load
const Avatar = React.lazy(() => import('@/components/Avatar'));

// Styles
import styles from './TestTakers.module.css';

//* ================================================================================================= */

// Types
interface TestTaker {
  id: number;
  name: string;
  lastname: string;
  email: string;
  token: string;
  avatar?: string;
  photo?: string; // New photo field from BunnyCDN
  server?: string;
  companies: string[];
  total_tokens: number;
}

interface SearchParams {
  keyword: string;
  test_counts: number | null;
  company_id: number | null;
}

interface PaginationState {
  page: number;
  pageSize: number;
}

// Query configuration for performance optimization
const QUERY_CONFIG = {
  staleTime: 1000 * 60 * 5, // 5 minutes - longer for test takers data
  gcTime: 1000 * 60 * 10, // 10 minutes cache time
  refetchOnWindowFocus: false, // Prevent unnecessary refetches
  refetchOnMount: false, // Use cached data when available
  retry: 2,
  retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
} as const;

const TestTakers: React.FC = () => {
  // Hooks
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { CompanyList, CompanyListLoading } = useCompanies();

  // State
  const [searchParams, setSearchParams] = React.useState<SearchParams>({
    keyword: '',
    test_counts: null,
    company_id: null
  });

  const [pagination, setPagination] = React.useState<PaginationState>({
    page: 1,
    pageSize: 25
  });

  // Debounced search params for API calls
  const [debouncedSearchParams, setDebouncedSearchParams] = React.useState<SearchParams>(searchParams);

  // Debounce search params to reduce API calls
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchParams(searchParams);
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [searchParams]);

  // Memoized fetch function with performance optimizations
  const fetchData = React.useCallback(async () => {
    try {
      // Only search if keyword is at least 3 characters or empty
      const effectiveKeyword = debouncedSearchParams.keyword && debouncedSearchParams.keyword.length < 3
        ? ''
        : debouncedSearchParams.keyword;

      const requestParams = {
        ...pagination,
        ...debouncedSearchParams,
        keyword: effectiveKeyword
      };

      console.log('[GetTestTakers] Fetching with params:', requestParams);

      const res = await api('GetTestTakers', requestParams);

      const { status, data } = res;

      if (status === 'error') {
        console.error('[GetTestTakers] API Error:', res);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: res.message || 'An error occurred while fetching data. Please try again later.',
          confirmButtonText: 'OK',
        });
        throw new Error('Failed to fetch TestTakers data');
      }

      console.log('[GetTestTakers] Success:', { total: data?.total, count: data?.data?.length });
      return data || {};
    } catch (error) {
      console.error('[GetTestTakers] Error fetching data:', error);
      return { data: [], total: 0 };
    }
  }, [pagination, debouncedSearchParams]);

  // React Query with optimized configuration and debounced search
  const { data, isFetching, refetch, isLoading } = useQuery({
    queryKey: ['GetTestTakers', pagination, debouncedSearchParams],
    queryFn: fetchData,
    enabled: true, // Always enabled, but debounced
    ...QUERY_CONFIG,
  });

  // Loading state that accounts for debouncing
  const isSearching = React.useMemo(() => {
    return isFetching || (searchParams.keyword !== debouncedSearchParams.keyword);
  }, [isFetching, searchParams.keyword, debouncedSearchParams.keyword]);

  // Memoized handlers
  const handleSearch = React.useCallback((values: SearchParams) => {
    setSearchParams(values);
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  const handleRefresh = React.useCallback(() => {
    refetch();
  }, [refetch]);

  const handleRowClick = React.useCallback((record: TestTaker) => {
    navigate(`${HOME_PATH}/tests/test-tokens/${record.token}`);
  }, [navigate]);

  const handlePaginationChange = React.useCallback((page: number, pageSize: number) => {
    setPagination({ page, pageSize });
  }, []);


  // Memoized table columns
  const columns = React.useMemo(() => [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      className: 'text-center',
      render: (_: any, __: any, index: number) => (
        <span className="text-muted fw-medium">
          {(pagination.page - 1) * pagination.pageSize + index + 1}
        </span>
      ),
    },
    {
      title: 'Test Taker',
      key: 'test_taker',
      render: (_: any, record: TestTaker) => (
        <div className="d-flex align-items-center">
          <div className="me-3">
            <React.Suspense fallback={
              <div className="rounded-circle bg-light d-flex align-items-center justify-content-center"
                style={{ width: 40, height: 40 }}>
                <UserOutlined className="text-muted" />
              </div>
            }>
              <Avatar
                token={record.token}
                avatar={record.avatar}
                photo={record.photo}
                rounded={true}
                size={40}
              />
            </React.Suspense>
          </div>
          <div>
            <div className="fw-semibold text-dark">
              {record.name} <span className="fw-bold">{uppCase(record.lastname)}</span>
            </div>
            <div className="text-muted small">
              Email: {record.email}
            </div>
            <div className="text-muted small">
              Token: {record.token}
            </div>
          </div>
        </div>
      ),
      sorter: (a: TestTaker, b: TestTaker) => a.name.localeCompare(b.name),
    },
    {
      title: 'Test Issuers',
      key: 'test-issuer',
      render: (_: any, record: TestTaker) => (
        <div>
          {record.companies?.length > 0 ? (
            record.companies.map((company, index) => (
              <Tag key={index} color="blue" bordered={false} className="mb-1">
                {company}
              </Tag>
            ))
          ) : (
            <span className="text-muted">No companies assigned</span>
          )}
        </div>
      ),
    },
    {
      title: 'Assigned Tests',
      dataIndex: 'total_tokens',
      key: 'total_tokens',
      width: 140,
      className: 'text-center',
      render: (total_tokens: number) => (
        <Tooltip title={`${total_tokens} test${total_tokens !== 1 ? 's' : ''} assigned`}>
          <Tag
            color={total_tokens > 0 ? 'green' : 'default'}
            bordered={false}
            icon={<FileTextOutlined />}
          >
            {total_tokens}
          </Tag>
        </Tooltip>
      ),
      sorter: (a: TestTaker, b: TestTaker) => a.total_tokens - b.total_tokens,
    },
  ], [pagination.page, pagination.pageSize]);



  // Memoized pagination config
  const paginationConfig = React.useMemo(() => ({
    pageSize: pagination.pageSize,
    current: pagination.page,
    total: data?.total || 0,
    showSizeChanger: true,
    pageSizeOptions: ['25', '50', '100'],
    showTotal: (total: number, range: [number, number]) => (
      <span className="text-muted">
        Showing <span className="fw-semibold">{range[0]}-{range[1]}</span> of{' '}
        <span className="fw-semibold">{total.toLocaleString()}</span> test takers
      </span>
    ),
    position: ['bottomCenter'] as const,
    onChange: handlePaginationChange,
    onShowSizeChange: handlePaginationChange,
  }), [pagination, data?.total, handlePaginationChange]);

  return (
    <div className={styles['test-takers-page']}>
      {/* Header */}
      <Card className="mb-4" bordered={false}>
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <div className={`d-flex align-items-center justify-content-center rounded-circle me-3 ${styles['header-icon-bg']}`}
              style={{ width: 48, height: 48 }}>
              <HiUsers className="text-primary" size={24} />
            </div>
            <div>
              <h2 className="mb-1 fw-bold">Test Takers</h2>
              <p className="text-muted mb-0">Manage and view all test takers</p>
            </div>
          </div>

          <Space>
            <Tooltip title="Refresh data">
              <Button
                type="text"
                icon={<AiOutlineReload />}
                onClick={handleRefresh}
                loading={isLoading}
                className="d-flex align-items-center"
              >
                Refresh
              </Button>
            </Tooltip>
          </Space>
        </div>
      </Card>

      {/* Search Form */}
      <Card className="mb-4" bordered={false}>
        <div className="d-flex align-items-center mb-3">
          <AiOutlineSearch className="text-primary me-2" size={20} />
          <h5 className="mb-0 fw-semibold">Search & Filter</h5>
        </div>

        <Form
          form={form}
          layout="vertical"
          initialValues={searchParams}
          onFinish={handleSearch}
          autoComplete="off"
        >

          <div className="row">
            <div className="col-lg-4 col-md-6">
              <Form.Item
                name="keyword"
                label={<span className="fw-semibold">Search by Name, Email or Token</span>}
                rules={[
                  {
                    min: 3,
                    message: 'Keyword must be at least 3 characters long',
                  },
                ]}
              >
                <Input
                  placeholder="Enter name, email or token..."
                  allowClear
                  variant="filled"
                  prefix={<AiOutlineSearch className="text-muted" />}
                />
              </Form.Item>
            </div>

            <div className="col-lg-4 col-md-6">
              <Form.Item
                name="company_id"
                label={<span className="fw-semibold">Test Issuer</span>}
              >
                {CompanyListLoading ? (
                  <Skeleton.Input active style={{ width: '100%', height: 32 }} />
                ) : (
                    <Select
                      variant="filled"
                      showSearch
                      allowClear
                      placeholder="All companies"
                      optionFilterProp="search"
                      options={CompanyList?.map((item: any) => ({
                        label: <div className="fw-semibold">{item.name}</div>,
                        value: item.id,
                        search: item.name,
                      }))}
                    />
                )}
              </Form.Item>
            </div>

            <div className="col-lg-4 col-md-6">
              <Form.Item
                name="test_counts"
                label={<span className="fw-semibold">Minimum Tests</span>}
              >
                <Select
                  variant="filled"
                  allowClear
                  placeholder="Any number"
                  options={Array.from({ length: 20 }, (_, index) => ({
                    label: <div className="fw-semibold">{index + 1}+ tests</div>,
                    value: index + 1,
                  }))}
                />
              </Form.Item>
            </div>
          </div>

          <div className="d-flex justify-content-end">
            <Space>
              <Button onClick={() => form.resetFields()}>
                Clear
              </Button>
              <Button type="primary" htmlType="submit" loading={isSearching}>
                Search
              </Button>
            </Space>
          </div>
        </Form>


      </Card>

      {/* Results Table */}
      <Card bordered={false}>
        <Spin spinning={isSearching}>
          <Table<TestTaker>
            size="middle"
            dataSource={data?.data}
            columns={columns}
            rowKey={(record) => `testtaker-${record.email}-${record.token}`}
            onRow={(record) => ({
              onClick: () => handleRowClick(record),
              className: `cursor-pointer ${styles['table-row-hover']}`,
            })}
            pagination={paginationConfig}
            locale={{
              emptyText: (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <span className="text-muted">
                      {searchParams.keyword || searchParams.company_id || searchParams.test_counts
                        ? 'No test takers found matching your criteria'
                        : 'No test takers available'}
                    </span>
                  }
                />
              ),
            }}
            scroll={{ x: 800 }}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default React.memo(TestTakers);