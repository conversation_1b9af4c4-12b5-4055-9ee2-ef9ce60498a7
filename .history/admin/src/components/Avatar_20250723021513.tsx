
import React from 'react';

// UI
import { Image } from 'antd';

// NPM
import { LazyLoadImage } from 'react-lazy-load-image-component';

// Icons
import { AiOutlineUser } from 'react-icons/ai';

interface AvatarProps {
  token: string;
  avatar?: string;
  photo?: string; // Photo field from BunnyCDN - ALWAYS takes priority if not null
  preview?: boolean;
  size?: number | null;
  rounded?: boolean;
}

const Avatar = React.memo((props: AvatarProps) => {
  const { token, avatar, photo, preview = false, size = null, rounded = false } = props;

  // Priority logic: photo field takes precedence over webcam/avatar field
  const avatarFile = photo || avatar;

  // Generate BunnyCDN URL only if we have a file
  const avatarUrl = React.useMemo(() => {
    return avatarFile
      ? `https://cepatest.b-cdn.net/${token}/${encodeURIComponent(avatarFile)}`
      : null;
  }, [token, avatarFile]);

  const NoAvatar = () => (
    <AiOutlineUser
      className={`border ${rounded ? 'rounded-circle' : ''}`}
      style={{
        width: size ? size : '100%',
        height: size ? size : 230,
        backgroundColor: '#F0F0F0',
        color: '#A0A0A0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    />
  );

  return (
    <div>
      {/* Display Logic: Show image if photo or avatar exists (always BunnyCDN) */}
      {avatarUrl ? (
        preview ? (
          <Image
            src={avatarUrl}
            className={`img-fluid candidate__Avatar ${rounded ? 'rounded-circle' : ''}`}
            alt='Candidate Avatar'
            style={size ? { width: size, height: size } : {}}
          />
        ) : (
          <LazyLoadImage
              src={avatarUrl}
              className={`img-fluid candidate__Avatar ${rounded ? 'rounded-circle' : ''}`}
              alt='Candidate Avatar'
              style={size ? { width: size, height: size } : {}}
            />
        )
      ) : (
        <NoAvatar />
      )}
    </div>
  );
});

Avatar.displayName = 'Avatar';

export default Avatar;