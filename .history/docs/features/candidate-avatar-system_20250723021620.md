# CEPA Candidate Avatar System

## Overview

The CEPA Candidate Avatar System provides a comprehensive solution for managing candidate profile photos with priority-based field handling, automatic file management, and optimized performance.

## Architecture

### Database Schema

The system uses two fields in the `oturum` table for avatar management:

- **`photo`** (VARCHAR): Primary avatar field - **ALWAYS takes priority**
- **`webcam`** (VARCHAR): Legacy avatar field - Used as fallback

### Priority Logic

```sql
-- Priority query pattern used throughout the system
SELECT 
  photo,
  webcam AS avatar,
  COALESCE(photo, webcam) AS primary_avatar
FROM oturum 
WHERE token = ?
```

**Priority Order:**
1. `photo` field (if not null/empty) - **HIGHEST PRIORITY**
2. `webcam` field (if not null/empty) - **FALLBACK**
3. Default avatar icon - **NO DATA**

## File Storage

### BunnyCDN Structure

```
https://cepatest.b-cdn.net/
├── {token}/
│   ├── {timestamp}_avatar.jpg    # New photo uploads
│   ├── old_avatar.jpg           # Legacy webcam files
│   └── WebCam/                  # Webcam videos (separate)
```

### URL Format

- **New Format**: `https://cepatest.b-cdn.net/{token}/{filename}`
- **Legacy Format**: `https://cepatest-system.b-cdn.net/cepatest/{token}/{filename}` (deprecated)

## API Endpoints

### Upload Avatar

**Endpoint**: `POST /api_admin2/upload-avatar`

**Features**:
- Automatic old file deletion
- Image processing and optimization
- Database update with priority field handling
- Error handling and rollback

**Request**:
```javascript
const formData = new FormData();
formData.append('file', imageBlob, 'avatar.jpg');
formData.append('token', candidateToken);

const response = await axios.post('/api_admin2/upload-avatar', formData, {
  headers: { 'Content-Type': 'multipart/form-data' }
});
```

**Response**:
```json
{
  "status": "success",
  "message": "Avatar uploaded and database updated successfully",
  "data": {
    "fileName": "1642678901234_avatar.jpg",
    "url": "https://cepatest.b-cdn.net/ABC123/1642678901234_avatar.jpg",
    "dbUpdate": { "status": "success", "updated_rows": 1 }
  }
}
```

### Update Candidate Photo

**Endpoint**: `POST /api_admin2/UpdateCandidatePhoto`

**Purpose**: Database-only update for photo filename

**Request**:
```json
{
  "token": "ABC123",
  "photo_filename": "1642678901234_avatar.jpg"
}
```

## Frontend Components

### Avatar Component

**Location**: `admin/src/components/Avatar.tsx`

**Features**:
- Priority-based field handling
- Performance optimized with React.memo
- Lazy loading support
- Responsive sizing
- Error handling with fallback

**Usage**:
```tsx
<Avatar
  token="ABC123"
  photo="1642678901234_avatar.jpg"  // Priority field
  avatar="legacy_avatar.jpg"        // Fallback field
  size={40}
  rounded={true}
  preview={false}
/>
```

### Upload Avatar Component

**Location**: `admin/src/pages/Candidate/Tabs/Info/UploadAvatar.tsx`

**Features**:
- Image cropping with aspect ratio options
- File validation and size limits
- Progress indicators
- Error handling
- Store integration

## Database Updates

### Backend Compatibility

All avatar update operations maintain backward compatibility:

```sql
-- New approach (recommended)
UPDATE oturum SET photo = ? WHERE token = ?

-- Legacy compatibility (both fields updated)
UPDATE oturum SET photo = ?, webcam = ? WHERE token = ?
```

### Query Patterns

**Get Candidate with Avatar Priority**:
```sql
SELECT 
  token,
  photo,
  webcam AS avatar,
  COALESCE(photo, webcam) AS primary_avatar,
  -- other fields...
FROM oturum 
WHERE token = ?
```

## File Management

### Automatic Cleanup

The system automatically handles old file cleanup:

1. **Before Upload**: Check for existing `photo` field value
2. **Delete Old**: Remove old file from BunnyCDN if exists
3. **Upload New**: Upload new file to BunnyCDN
4. **Update DB**: Update `photo` field with new filename

### Error Handling

- Upload failures don't affect existing data
- Database rollback on CDN upload failure
- Graceful degradation for missing files
- Comprehensive logging for debugging

## Performance Optimizations

### Frontend

- **React.memo**: Avatar component memoization
- **useMemo**: URL generation caching
- **Lazy Loading**: Image loading optimization
- **Error Boundaries**: Graceful failure handling

### Backend

- **Single Query**: Priority logic in SQL
- **Batch Operations**: Combined file and DB updates
- **Connection Pooling**: Database optimization
- **CDN Caching**: Automatic image caching

## Migration Guide

### From Legacy System

1. **Database**: No migration needed - both fields maintained
2. **Frontend**: Update components to use new Avatar component
3. **API**: Gradually migrate to new upload endpoint
4. **URLs**: System handles both old and new URL formats

### Best Practices

1. **Always use** the new Avatar component for consistency
2. **Prefer photo field** over webcam field in new code
3. **Handle errors gracefully** with fallback avatars
4. **Optimize images** before upload (handled automatically)
5. **Test thoroughly** with both field scenarios

## Troubleshooting

### Common Issues

1. **Avatar not displaying**: Check both photo and webcam fields
2. **Upload failures**: Verify BunnyCDN credentials and permissions
3. **Old files not deleted**: Check CDN access keys and file permissions
4. **Performance issues**: Verify image optimization settings

### Debug Information

Enable development logging:
```javascript
// In development mode, Avatar component logs priority decisions
console.log('[Avatar] Priority logic:', {
  photo: photo || 'null',
  avatar: avatar || 'null',
  selectedSource: photo ? 'photo (priority)' : 'avatar (fallback)',
  finalUrl: avatarUrl
});
```

## Security Considerations

- File type validation (images only)
- File size limits (5MB max)
- Secure CDN access with proper keys
- Input sanitization for filenames
- CORS configuration for uploads

---

**Last Updated**: January 2025  
**Version**: 2.0.0  
**Maintainer**: CEPA Development Team
